import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo mới API key của người dùng
 */
export class CreateUserKeyDto {
  @ApiProperty({
    description: 'ID của nhà cung cấp AI',
    example: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  providerId: number;

  @ApiProperty({
    description: 'Thông tin xác thực API',
    example: {
      api_key: 'sk-abcdefghijklmnopqrstuvwxyz',
      organization_id: 'org-123456789'
    }
  })
  @IsNotEmpty()
  @IsObject()
  credentials: Record<string, any>;

  @ApiProperty({
    description: 'Cài đặt người dùng',
    example: {
      is_active: true,
      is_default: true
    },
    required: false
  })
  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;
}
