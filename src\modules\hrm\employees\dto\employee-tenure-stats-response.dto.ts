import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thống kê thâm niên nhân viên
 */
export class EmployeeTenureStatsResponseDto {
  /**
   * Thâm niên trung bình (tính bằng năm)
   * @example 2.8
   */
  @ApiProperty({
    description: 'Thâm niên trung bình (tính bằng năm)',
    example: 2.8,
  })
  averageTenureYears: number;

  /**
   * Số lượng nhân viên sắp hết thời gian thử việc (trong vòng 30 ngày tới)
   * @example 5
   */
  @ApiProperty({
    description: 'Số lượng nhân viên sắp hết thời gian thử việc (trong vòng 30 ngày tới)',
    example: 5,
  })
  probationEndingSoon: number;

  /**
   * Tỷ lệ tăng trưởng nhân viên theo thán<PERSON> (%)
   * @example 6.4
   */
  @ApiProperty({
    description: 'Tỷ lệ tăng trưởng nhân viên theo thán<PERSON> (%)',
    example: 6.4,
  })
  monthlyGrowthRate: number;

  /**
   * Tổng số nhân viên hiện tại
   * @example 125
   */
  @ApiProperty({
    description: 'Tổng số nhân viên hiện tại',
    example: 125,
  })
  totalEmployees: number;

  /**
   * Số nhân viên mới tháng này
   * @example 8
   */
  @ApiProperty({
    description: 'Số nhân viên mới tháng này',
    example: 8,
  })
  newEmployeesThisMonth: number;

  /**
   * Số nhân viên mới tháng trước
   * @example 5
   */
  @ApiProperty({
    description: 'Số nhân viên mới tháng trước',
    example: 5,
  })
  newEmployeesLastMonth: number;

  /**
   * Thống kê chi tiết theo khoảng thâm niên
   */
  @ApiProperty({
    description: 'Thống kê chi tiết theo khoảng thâm niên',
    example: {
      lessThan1Year: 25,
      oneToThreeYears: 45,
      threeToFiveYears: 35,
      moreThanFiveYears: 20,
    },
  })
  tenureBreakdown: {
    lessThan1Year: number;
    oneToThreeYears: number;
    threeToFiveYears: number;
    moreThanFiveYears: number;
  };
}
