import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Employee } from '../entities/employee.entity';
import { EmployeeQueryDto } from '../dto/employee-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';
import { EmployeeStatus } from '../enum/employee-status.enum';

/**
 * Repository for employee entity
 */
@Injectable()
export class EmployeeRepository {
  private readonly logger = new Logger(EmployeeRepository.name);

  constructor(
    @InjectRepository(Employee)
    private readonly repository: Repository<Employee>,
  ) {}

  /**
   * Find all employees with pagination and filtering
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Query parameters
   * @returns Paginated list of employees
   */
  async findAll(
    tenantId: number,
    query: EmployeeQueryDto,
  ): Promise<PaginatedResult<Employee>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'employeeCode',
      sortDirection = 'ASC',
      departmentId,
      managerId,
      status,
      employmentType,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('employee');

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('employee.tenantId = :tenantId', { tenantId });

    // Apply filters if provided
    if (departmentId) {
      queryBuilder.andWhere('employee.departmentId = :departmentId', {
        departmentId,
      });
    }

    if (managerId) {
      queryBuilder.andWhere('employee.managerId = :managerId', { managerId });
    }

    if (status) {
      queryBuilder.andWhere('employee.status = :status', { status });
    }

    if (employmentType) {
      queryBuilder.andWhere('employee.employmentType = :employmentType', {
        employmentType,
      });
    }

    // Apply search filter if provided - search both employeeCode and employeeName
    if (search) {
      queryBuilder.andWhere(
        '(employee.employeeCode ILIKE :search OR employee.employeeName ILIKE :search)',
        {
          search: `%${search}%`,
        },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`employee.${sortBy}`, sortDirection);

    // Apply pagination
    const [items, totalItems] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Find employee by ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @returns Employee or null if not found
   */
  async findById(tenantId: number, id: number): Promise<Employee | null> {
    return this.repository.findOne({
      where: { id, tenantId },
    });
  }

  /**
   * Find employee by employee code
   * @param tenantId ID tenant (required for tenant isolation)
   * @param employeeCode Employee code
   * @returns Employee or null if not found
   */
  async findByEmployeeCode(
    tenantId: number,
    employeeCode: string,
  ): Promise<Employee | null> {
    return this.repository.findOne({
      where: { employeeCode, tenantId },
    });
  }

  /**
   * Find employees by department ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param departmentId Department ID
   * @returns List of employees
   */
  async findByDepartmentId(
    tenantId: number,
    departmentId: number,
  ): Promise<Employee[]> {
    return this.repository.find({
      where: { departmentId, tenantId },
      order: { employeeCode: 'ASC' },
    });
  }

  /**
   * Find employees by manager ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param managerId Manager ID
   * @returns List of employees
   */
  async findByManagerId(
    tenantId: number,
    managerId: number,
  ): Promise<Employee[]> {
    return this.repository.find({
      where: { managerId, tenantId },
      order: { employeeCode: 'ASC' },
    });
  }

  /**
   * Create a new employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Employee data
   * @returns Created employee
   */
  async create(tenantId: number, data: Partial<Employee>): Promise<Employee> {
    const employee = this.repository.create({ ...data, tenantId });
    return this.repository.save(employee);
  }

  /**
   * Update employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @param data Updated employee data
   * @returns Updated employee or null if not found
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<Employee>,
  ): Promise<Employee | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Delete employee
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @returns True if deleted, false if not found
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Update employee status
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id Employee ID
   * @param status New status
   * @returns Updated employee or null if not found
   */
  async updateStatus(
    tenantId: number,
    id: number,
    status: EmployeeStatus,
  ): Promise<Employee | null> {
    await this.repository.update({ id, tenantId }, { status });
    return this.findById(tenantId, id);
  }

  /**
   * Count employees by department
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns List of department IDs with employee counts
   */
  async countByDepartment(
    tenantId: number,
  ): Promise<{ departmentId: number; count: number }[]> {
    const result = await this.repository
      .createQueryBuilder('employee')
      .select('employee.departmentId', 'departmentId')
      .addSelect('COUNT(employee.id)', 'count')
      .where(
        'employee.departmentId IS NOT NULL AND employee.tenantId = :tenantId',
        { tenantId },
      )
      .groupBy('employee.departmentId')
      .getRawMany();

    return result.map((item) => ({
      departmentId: parseInt(item.departmentId),
      count: parseInt(item.count),
    }));
  }

  /**
   * Tìm mã nhân viên lớn nhất có format REDAI + số
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Số lớn nhất trong mã nhân viên hoặc 0 nếu chưa có nhân viên nào
   */
  async findMaxEmployeeCodeNumber(tenantId: number): Promise<number> {
    try {
      // Sử dụng raw query để tìm số lớn nhất hiệu quả hơn
      const result = await this.repository
        .createQueryBuilder('employee')
        .select('MAX(CAST(SUBSTRING(employee.employeeCode FROM 6) AS INTEGER))', 'maxNumber')
        .where('employee.tenantId = :tenantId', { tenantId })
        .andWhere('employee.employeeCode ~ :pattern', { pattern: '^REDAI[0-9]+$' })
        .getRawOne();

      // Nếu không có kết quả hoặc maxNumber là null, trả về 0
      const maxNumber = result?.maxNumber;
      return maxNumber && !isNaN(parseInt(maxNumber)) ? parseInt(maxNumber) : 0;
    } catch (error) {
      this.logger.error(
        `Error finding max employee code number: ${error.message}`,
        error.stack,
      );

      // Fallback: Sử dụng phương pháp cũ nếu raw query không hoạt động
      try {
        const fallbackResult = await this.repository
          .createQueryBuilder('employee')
          .select('employee.employeeCode', 'employeeCode')
          .where('employee.tenantId = :tenantId', { tenantId })
          .andWhere('employee.employeeCode LIKE :pattern', { pattern: 'REDAI%' })
          .getMany();

        if (!fallbackResult || fallbackResult.length === 0) {
          return 0;
        }

        // Tìm số lớn nhất từ các mã nhân viên
        let maxNumber = 0;
        for (const employee of fallbackResult) {
          const codeNumber = employee.employeeCode.replace('REDAI', '');
          const number = parseInt(codeNumber, 10);
          if (!isNaN(number) && number > maxNumber) {
            maxNumber = number;
          }
        }

        return maxNumber;
      } catch (fallbackError) {
        this.logger.error(
          `Fallback method also failed: ${fallbackError.message}`,
          fallbackError.stack,
        );
        return 0;
      }
    }
  }

  /**
   * Tìm nhiều nhân viên theo danh sách ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID nhân viên
   * @returns Danh sách nhân viên tìm thấy
   */
  async findByIds(tenantId: number, ids: number[]): Promise<Employee[]> {
    if (ids.length === 0) return [];

    return this.repository.find({
      where: {
        id: In(ids),
        tenantId,
      },
    });
  }

  /**
   * Xóa nhiều nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID nhân viên cần xóa
   * @returns Số lượng nhân viên đã xóa thành công
   */
  async bulkDelete(tenantId: number, ids: number[]): Promise<number> {
    if (ids.length === 0) return 0;

    const result = await this.repository.delete({
      id: In(ids),
      tenantId,
    });

    return result.affected ?? 0;
  }

  /**
   * Lấy thống kê tổng quan nhân viên cho tenant
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Object chứa các thống kê cơ bản
   */
  async getEmployeeOverviewStats(tenantId: number): Promise<{
    totalEmployees: number;
    activeEmployees: number;
    inactiveEmployees: number;
    probationEmployees: number;
    newEmployeesThisMonth: number;
  }> {
    try {
      // Tính toán ngày đầu và cuối tháng hiện tại
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      // Query thống kê với một lần gọi database
      const stats = await this.repository
        .createQueryBuilder('employee')
        .select([
          'COUNT(*) as total_employees',
          'COUNT(CASE WHEN employee.status = :activeStatus THEN 1 END) as active_employees',
          'COUNT(CASE WHEN employee.status = :inactiveStatus THEN 1 END) as inactive_employees',
          'COUNT(CASE WHEN employee.status = :probationStatus THEN 1 END) as probation_employees',
          'COUNT(CASE WHEN employee.hireDate >= :startOfMonth AND employee.hireDate <= :endOfMonth THEN 1 END) as new_employees_this_month',
        ])
        .where('employee.tenantId = :tenantId', { tenantId })
        .setParameters({
          activeStatus: 'active',
          inactiveStatus: 'inactive',
          probationStatus: 'probation',
          startOfMonth: startOfMonth.toISOString().split('T')[0], // Format YYYY-MM-DD
          endOfMonth: endOfMonth.toISOString().split('T')[0],
        })
        .getRawOne();

      return {
        totalEmployees: parseInt(stats.total_employees) || 0,
        activeEmployees: parseInt(stats.active_employees) || 0,
        inactiveEmployees: parseInt(stats.inactive_employees) || 0,
        probationEmployees: parseInt(stats.probation_employees) || 0,
        newEmployeesThisMonth: parseInt(stats.new_employees_this_month) || 0,
      };
    } catch (error) {
      this.logger.error(
        `Error getting employee overview stats: ${error.message}`,
        error.stack,
      );
      // Trả về stats mặc định nếu có lỗi
      return {
        totalEmployees: 0,
        activeEmployees: 0,
        inactiveEmployees: 0,
        probationEmployees: 0,
        newEmployeesThisMonth: 0,
      };
    }
  }

  /**
   * Lấy thống kê phân bố nhân viên theo phòng ban
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Thống kê phân bố theo phòng ban
   */
  async getDepartmentDistribution(tenantId: number): Promise<{
    totalEmployees: number;
    departments: Array<{
      departmentId: number | null;
      departmentName: string;
      employeeCount: number;
    }>;
    unassignedEmployees: number;
  }> {
    try {
      // Lấy tổng số nhân viên
      const totalEmployees = await this.repository
        .createQueryBuilder('employee')
        .where('employee.tenantId = :tenantId', { tenantId })
        .getCount();

      // Lấy thống kê theo phòng ban (có departmentId)
      const departmentStats = await this.repository
        .createQueryBuilder('employee')
        .leftJoin('departments', 'dept', 'dept.id = employee.departmentId AND dept.tenantId = :tenantId')
        .select([
          'employee.departmentId as departmentId',
          'dept.name as departmentName',
          'COUNT(employee.id) as employeeCount',
        ])
        .where('employee.tenantId = :tenantId')
        .andWhere('employee.departmentId IS NOT NULL')
        .groupBy('employee.departmentId, dept.name')
        .orderBy('employeeCount', 'DESC')
        .setParameter('tenantId', tenantId)
        .getRawMany();

      // Đếm nhân viên chưa được phân phòng ban
      const unassignedEmployees = await this.repository
        .createQueryBuilder('employee')
        .where('employee.tenantId = :tenantId', { tenantId })
        .andWhere('employee.departmentId IS NULL')
        .getCount();

      // Debug logging
      this.logger.debug(`Department stats raw result:`, departmentStats);

      return {
        totalEmployees,
        departments: departmentStats.map(stat => ({
          departmentId: stat.departmentid ? parseInt(stat.departmentid) : null,  // PostgreSQL trả về lowercase
          departmentName: stat.departmentname || 'Không xác định',  // PostgreSQL trả về lowercase
          employeeCount: stat.employeecount ? parseInt(stat.employeecount) : 0,  // PostgreSQL trả về lowercase
        })),
        unassignedEmployees,
      };
    } catch (error) {
      this.logger.error(
        `Error getting department distribution: ${error.message}`,
        error.stack,
      );
      return {
        totalEmployees: 0,
        departments: [],
        unassignedEmployees: 0,
      };
    }
  }

  /**
   * Lấy thống kê phân bố nhân viên theo loại hợp đồng (employment_type)
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Thống kê phân bố theo loại hợp đồng
   */
  async getContractDistribution(tenantId: number): Promise<{
    totalEmployees: number;
    contracts: Array<{
      contractType: string;
      employeeCount: number;
    }>;
    noContractEmployees: number;
  }> {
    try {
      // Lấy tổng số nhân viên
      const totalEmployees = await this.repository
        .createQueryBuilder('employee')
        .where('employee.tenantId = :tenantId', { tenantId })
        .getCount();

      // Lấy thống kê theo employment_type
      const contractStats = await this.repository
        .createQueryBuilder('employee')
        .select([
          'employee.employmentType as contractType',
          'COUNT(employee.id) as employeeCount',
        ])
        .where('employee.tenantId = :tenantId', { tenantId })
        .andWhere('employee.employmentType IS NOT NULL')
        .groupBy('employee.employmentType')
        .orderBy('employeeCount', 'DESC')
        .getRawMany();

      // Đếm nhân viên chưa có employment_type
      const noContractEmployees = await this.repository
        .createQueryBuilder('employee')
        .where('employee.tenantId = :tenantId', { tenantId })
        .andWhere('employee.employmentType IS NULL')
        .getCount();

      // Debug logging
      this.logger.debug(`Contract stats raw result:`, contractStats);
      this.logger.debug(`Total employees: ${totalEmployees}, No contract employees: ${noContractEmployees}`);

      return {
        totalEmployees,
        contracts: contractStats.map(stat => ({
          contractType: stat.contracttype,  // PostgreSQL trả về lowercase
          employeeCount: stat.employeecount ? parseInt(stat.employeecount) : 0,  // PostgreSQL trả về lowercase
        })),
        noContractEmployees,
      };
    } catch (error) {
      this.logger.error(
        `Error getting contract distribution: ${error.message}`,
        error.stack,
      );
      return {
        totalEmployees: 0,
        contracts: [],
        noContractEmployees: 0,
      };
    }
  }

  /**
   * Lấy thống kê thâm niên nhân viên
   * @param tenantId ID tenant (required for tenant isolation)
   * @returns Thống kê thâm niên
   */
  async getTenureStats(tenantId: number): Promise<{
    totalEmployees: number;
    averageTenureYears: number;
    probationEndingSoon: number;
    newEmployeesThisMonth: number;
    newEmployeesLastMonth: number;
    tenureBreakdown: {
      lessThan1Year: number;
      oneToThreeYears: number;
      threeToFiveYears: number;
      moreThanFiveYears: number;
    };
  }> {
    try {
      const now = new Date();

      // Tính toán các mốc thời gian
      const startOfThisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfThisMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
      const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

      // Các mốc thời gian cho tenure breakdown
      const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
      const threeYearsAgo = new Date(now.getFullYear() - 3, now.getMonth(), now.getDate());
      const fiveYearsAgo = new Date(now.getFullYear() - 5, now.getMonth(), now.getDate());

      // Query tổng hợp
      const stats = await this.repository
        .createQueryBuilder('employee')
        .select([
          'COUNT(*) as total_employees',
          'AVG(EXTRACT(EPOCH FROM (CURRENT_DATE - employee.hireDate)) / (365.25 * 24 * 3600)) as avg_tenure_years',
          'COUNT(CASE WHEN employee.probationEndDate IS NOT NULL AND employee.probationEndDate <= :thirtyDaysFromNow AND employee.probationEndDate >= CURRENT_DATE THEN 1 END) as probation_ending_soon',
          'COUNT(CASE WHEN employee.hireDate >= :startOfThisMonth AND employee.hireDate <= :endOfThisMonth THEN 1 END) as new_employees_this_month',
          'COUNT(CASE WHEN employee.hireDate >= :startOfLastMonth AND employee.hireDate <= :endOfLastMonth THEN 1 END) as new_employees_last_month',
          'COUNT(CASE WHEN employee.hireDate > :oneYearAgo THEN 1 END) as less_than_1_year',
          'COUNT(CASE WHEN employee.hireDate <= :oneYearAgo AND employee.hireDate > :threeYearsAgo THEN 1 END) as one_to_three_years',
          'COUNT(CASE WHEN employee.hireDate <= :threeYearsAgo AND employee.hireDate > :fiveYearsAgo THEN 1 END) as three_to_five_years',
          'COUNT(CASE WHEN employee.hireDate <= :fiveYearsAgo THEN 1 END) as more_than_five_years',
        ])
        .where('employee.tenantId = :tenantId', { tenantId })
        .andWhere('employee.hireDate IS NOT NULL')
        .setParameters({
          thirtyDaysFromNow: thirtyDaysFromNow.toISOString().split('T')[0],
          startOfThisMonth: startOfThisMonth.toISOString().split('T')[0],
          endOfThisMonth: endOfThisMonth.toISOString().split('T')[0],
          startOfLastMonth: startOfLastMonth.toISOString().split('T')[0],
          endOfLastMonth: endOfLastMonth.toISOString().split('T')[0],
          oneYearAgo: oneYearAgo.toISOString().split('T')[0],
          threeYearsAgo: threeYearsAgo.toISOString().split('T')[0],
          fiveYearsAgo: fiveYearsAgo.toISOString().split('T')[0],
        })
        .getRawOne();

      return {
        totalEmployees: parseInt(stats.total_employees) || 0,
        averageTenureYears: parseFloat(stats.avg_tenure_years) || 0,
        probationEndingSoon: parseInt(stats.probation_ending_soon) || 0,
        newEmployeesThisMonth: parseInt(stats.new_employees_this_month) || 0,
        newEmployeesLastMonth: parseInt(stats.new_employees_last_month) || 0,
        tenureBreakdown: {
          lessThan1Year: parseInt(stats.less_than_1_year) || 0,
          oneToThreeYears: parseInt(stats.one_to_three_years) || 0,
          threeToFiveYears: parseInt(stats.three_to_five_years) || 0,
          moreThanFiveYears: parseInt(stats.more_than_five_years) || 0,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting tenure stats: ${error.message}`,
        error.stack,
      );
      return {
        totalEmployees: 0,
        averageTenureYears: 0,
        probationEndingSoon: 0,
        newEmployeesThisMonth: 0,
        newEmployeesLastMonth: 0,
        tenureBreakdown: {
          lessThan1Year: 0,
          oneToThreeYears: 0,
          threeToFiveYears: 0,
          moreThanFiveYears: 0,
        },
      };
    }
  }
}
