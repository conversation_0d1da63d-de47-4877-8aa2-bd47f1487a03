import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, Max, Min } from 'class-validator';

/**
 * DTO cho việc tạo mới cấu hình máy chủ email
 */
export class CreateEmailServerDto {
  @ApiProperty({
    description: 'Tên hiển thị của cấu hình, ví dụ: "Mailgun Server #1" hoặc "AWS SES"',
    example: 'Gmail SMTP',
  })
  @IsNotEmpty({ message: 'Tên máy chủ không được để trống' })
  @IsString({ message: 'Tên máy chủ phải là chuỗi' })
  serverName: string;

  @ApiProperty({
    description: 'Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…',
    example: 'smtp.gmail.com',
  })
  @IsNotEmpty({ message: 'Đ<PERSON><PERSON> chỉ máy chủ không được để trống' })
  @IsString({ message: 'Địa chỉ máy chủ phải là chuỗi' })
  host: string;

  @ApiProperty({
    description: 'C<PERSON>ng SMTP, ví dụ: 465, 587, …',
    example: 587,
  })
  @IsNotEmpty({ message: 'Cổng không được để trống' })
  @IsNumber({}, { message: 'Cổng phải là số' })
  @Min(1, { message: 'Cổng phải lớn hơn 0' })
  @Max(65535, { message: 'Cổng không được vượt quá 65535' })
  port: number;

  @ApiProperty({
    description: 'Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng)',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: 'Tên đăng nhập không được để trống' })
  @IsString({ message: 'Tên đăng nhập phải là chuỗi' })
  username: string;

  @ApiProperty({
    description: 'Mật khẩu hoặc token xác thực cho SMTP',
    example: 'app-password-or-token',
  })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  password: string;

  @ApiProperty({
    description: 'Xác định có sử dụng SSL/TLS hay không',
    example: true,
  })
  @IsNotEmpty({ message: 'Trường useSsl không được để trống' })
  @IsBoolean({ message: 'Trường useSsl phải là boolean' })
  useSsl: boolean;

  @ApiProperty({
    description: 'Xác định có sử dụng STARTTLS hay không',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường useStartTls phải là boolean' })
  useStartTls?: boolean;

  @ApiProperty({
    description: 'Trạng thái hoạt động của cấu hình',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'Trường isActive phải là boolean' })
  isActive?: boolean;

  @ApiProperty({
    description: 'Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.',
    example: { auth: 'login', tls: { rejectUnauthorized: false } },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Cấu hình nâng cao phải là đối tượng' })
  additionalSettings?: Record<string, any>;
}
