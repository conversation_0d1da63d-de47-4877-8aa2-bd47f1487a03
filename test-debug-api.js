const axios = require('axios');

// Test debug API
async function testDebugAPI() {
  try {
    console.log('=== TESTING DEBUG API ===\n');

    // Thay đổi token này với token thực tế của bạn
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'; // Thay bằng token thực tế

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Test debug statistics
    console.log('1. Testing debug statistics...');
    const debugResponse = await axios.get('http://localhost:3001/api/hrm/employees/debug/statistics', { headers });
    console.log('Debug Response:', JSON.stringify(debugResponse.data, null, 2));

    console.log('\n2. Testing department distribution...');
    const deptResponse = await axios.get('http://localhost:3001/api/hrm/employees/statistics/department-distribution', { headers });
    console.log('Department Distribution Response:', JSON.stringify(deptResponse.data, null, 2));

    console.log('\n3. Testing contract distribution...');
    const contractResponse = await axios.get('http://localhost:3001/api/hrm/employees/statistics/contract-distribution', { headers });
    console.log('Contract Distribution Response:', JSON.stringify(contractResponse.data, null, 2));

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testDebugAPI();
