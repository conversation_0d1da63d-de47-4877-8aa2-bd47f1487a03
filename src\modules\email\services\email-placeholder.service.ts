import { Injectable, Logger } from '@nestjs/common';
import { AppException, ErrorCode } from '@/common';
import {
  AffiliateWithdrawRejectedPlaceholder,
  AccountRegistrationSuccessPlaceholder,
  TwoFAPlaceholder,
  AffiliateRankUpPlaceholder,
  TicketLowRatingPlaceholder,
  PaymentSuccessPlaceholder,
  EmailVerificationPlaceholder,
  FeatureSuggestionThankYouPlaceholder,
  DiscountCampaignPlaceholder,
  AssistantInactivePlaceholder,
  EmployeeFeatureSuggestionPlaceholder,
  PaymentGatewayIntegrationPlaceholder,
  WebsiteIntegrationPlaceholder,
  BadExperienceApologyPlaceholder,
  RuleContractReminderPlaceholder,
  AffiliatePriorityBenefitPlaceholder,
  AffiliateRankUpRewardPlaceholder,
  FacebookIntegrationPlaceholder,
  NewRuleContractProcessingPlaceholder,
  BusinessInfoUpdateProcessingPlaceholder,
  RuleContractSignFailedPlaceholder,
  AffiliateRegistrationProcessingPlaceholder,
  NewComplaintPlaceholder,
  NewInvoiceProcessingPlaceholder,
  AffiliateContractSignedPlaceholder,
  CustomerImportSuccessPlaceholder,
  AffiliateRegistrationRejectedPlaceholder,
  AffiliateDiscountCodePlaceholder,
  AffiliateWithdrawProcessedIndividualPlaceholder,
  AffiliateWithdrawProcessedBusinessPlaceholder,
  SystemMaintenancePlaceholder,
  PaymentGatewayDeletionPlaceholder,
  AccountReactivationPlaceholder,
  EmployeeTwoFAPlaceholder,
  EmployeeAccountCreationPlaceholder,
  PolicyViolationProductPlaceholder,
  BusinessInfoChangeConfirmationPlaceholder,
  AffiliateRegistrationSuccessPlaceholder,
  AffiliateWithdrawProcessingPlaceholder,
  VatInvoiceIssuancePlaceholder,
  NewAffiliateContractProcessingPlaceholder,
  RuleContractSignedPlaceholder,
  RuleContractProcessingPlaceholder,
  AdminLoginInfoPlaceholder,
  FeatureSuggestionResponsePlaceholder,
  ComplaintReceivedPlaceholder,
  AffiliateRankDownPlaceholder,
  AffiliatePRBenefitPlaceholder,
  CartPaymentReminderPlaceholder,
  ExclusiveDiscountCodePlaceholder,
  BankInfoChangeSuccessPlaceholder,
  BankInfoUpdateProcessingPlaceholder,
  LowBalancePlaceholder,
  InactiveAccountPlaceholder,
  NewFeatureUpdatePlaceholder,
  ResourceViolationDeletionPlaceholder,
  ZaloIntegrationPlaceholder,
  QualityPostNotificationPlaceholder,
  TicketRatingReminderPlaceholder,
  ForgotPasswordPlaceholder,
  IssueResolvedPlaceholder,
  CustomerImportFailedPlaceholder,
  ProductImportSuccessPlaceholder,
  ProductImportFailedPlaceholder,
  TicketFiveStarRatingPlaceholder,
  SystemIssuePlaceholder,
  EmployeeForgotPasswordPlaceholder,
  AIAutomationCourseGiftPlaceholder,
  AIPremiumContentUnlockPlaceholder,
  RuleContractOTPSigningPlaceholder,
  AccountSuspensionPlaceholder,
  AffiliateContractOTPSigningPlaceholder,
} from '../enum/email-placeholder.enum';
import { EmailClientService } from './email-client.service';
import {
  EmailSystemQueueService,
  EmailSystemJobDto,
} from '@shared/queue/email-system-queue.service';
import { CategoryTemplateAutoEnum } from '../enum/category-template-auto.enum';

/**
 * Service xử lý các email với placeholder dựa trên các enum
 */
@Injectable()
export class EmailPlaceholderService {
  private readonly logger = new Logger(EmailPlaceholderService.name);

  constructor(
    private readonly emailService: EmailClientService,
    private readonly emailSystemQueueService: EmailSystemQueueService,
  ) {}

  /**
   * Gửi email đăng ký tài khoản thành công
   * @param to Địa chỉ email người nhận
   * @param data Object chứa các giá trị tương ứng với AccountRegistrationSuccessPlaceholder
   * @returns Promise với job ID
   */
  async sendAccountRegistrationSuccess(
    to: string,
    data: Partial<
      Record<
        keyof typeof AccountRegistrationSuccessPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra email có tồn tại không
      if (!to) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo đăng ký tài khoản thành công',
        );
      }

      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo đăng ký tài khoản thành công',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email thông báo đăng ký tài khoản thành công đến: ${to}`,
      );

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['NAME']);

      // Tạo EmailSystemJobDto
      const emailData: EmailSystemJobDto = {
        category: CategoryTemplateAutoEnum.ACCOUNT_REGISTRATION_SUCCESSFUL,
        to: to,
        data: cleanData,
      };

      return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(
        emailData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email thông báo đăng ký tài khoản thành công: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email thông báo đăng ký tài khoản thành công',
          );
    }
  }

  /**
   * Gửi email xác minh 2 lớp
   * @param to Địa chỉ email người nhận
   * @param data Object chứa các giá trị tương ứng với TwoFAPlaceholder
   * @returns Promise với job ID
   */
  async sendTwoFAVerification(
    to: string,
    data: Partial<
      Record<keyof typeof TwoFAPlaceholder, string | null | undefined>
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra email có tồn tại không
      if (!to) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi xác minh 2 lớp',
        );
      }

      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email xác minh 2 lớp',
        );
      }

      if (!data.TWO_FA_CODE) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu mã xác thực 2FA để gửi email',
        );
      }

      this.logger.log(`Chuẩn bị gửi email xác minh 2 lớp đến: ${to}`);

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['TWO_FA_CODE']);

      // Tạo EmailSystemJobDto
      const emailData: EmailSystemJobDto = {
        category: CategoryTemplateAutoEnum.ACCOUNT_2FA,
        to: to,
        data: cleanData,
      };

      // Sử dụng độ ưu tiên cao hơn cho mã xác thực
      return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(
        emailData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email xác minh 2 lớp: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email xác minh 2 lớp',
          );
    }
  }

  /**
   * Gửi email thông báo đánh giá ticket dưới 3 sao
   * @param data Object chứa các giá trị tương ứng với TicketLowRatingPlaceholder
   * @returns Promise với job ID
   */
  async sendTicketLowRating(
    data: Partial<
      Record<keyof typeof TicketLowRatingPlaceholder, string | null | undefined>
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo đánh giá ticket dưới 3 sao',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo đánh giá ticket dưới 3 sao',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email thông báo đánh giá ticket dưới 3 sao đến: ${data.EMAIL}`,
      );

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'EMPLOYEE_NAME']);

      // Tạo EmailSystemJobDto
      const emailData: EmailSystemJobDto = {
        category: CategoryTemplateAutoEnum.OPERATION_TICKET_LOW_RATING,
        to: cleanData.EMAIL,
        data: cleanData,
      };

      return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(
        emailData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email thông báo đánh giá ticket dưới 3 sao: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email thông báo đánh giá ticket dưới 3 sao',
          );
    }
  }

  /**
   * Gửi email thông báo thanh toán thành công
   * @param data Object chứa các giá trị tương ứng với PaymentSuccessPlaceholder
   * @returns Promise với job ID
   */
  async sendPaymentSuccess(
    data: Partial<
      Record<keyof typeof PaymentSuccessPlaceholder, string | null | undefined>
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo thanh toán thành công',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo thanh toán thành công',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email thông báo thanh toán thành công đến: ${data.EMAIL}`,
      );

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME', 'ORDER_ID']);

      // Tạo EmailSystemJobDto
      const emailData: EmailSystemJobDto = {
        category: CategoryTemplateAutoEnum.PAYMENT_SUCCESSFUL,
        to: cleanData.EMAIL,
        data: cleanData,
      };

      return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(
        emailData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email thông báo thanh toán thành công: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email thông báo thanh toán thành công',
          );
    }
  }

  /**
   * Gửi email xác thực tài khoản
   * @param data Object chứa các giá trị tương ứng với EmailVerificationPlaceholder
   * @returns Promise với job ID
   */
  async sendEmailVerification(
    data: Partial<
      Record<
        keyof typeof EmailVerificationPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email xác thực tài khoản',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email xác thực tài khoản',
        );
      }

      // Kiểm tra mã xác thực có tồn tại không
      if (!data.TWO_FA_CODE) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu mã xác thực để gửi email xác thực tài khoản',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email xác thực tài khoản đến: ${data.EMAIL}`,
      );

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME', 'TWO_FA_CODE']);

      // Tạo EmailSystemJobDto
      const emailData: EmailSystemJobDto = {
        category: CategoryTemplateAutoEnum.ACCOUNT_VERIFICATION_EMAIL,
        to: cleanData.EMAIL,
        data: cleanData,
      };

      return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(
        emailData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email xác thực tài khoản: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email xác thực tài khoản',
          );
    }
  }

  /**
   * Gửi email cảm ơn đề xuất tính năng
   * @param data Object chứa các giá trị tương ứng với FeatureSuggestionThankYouPlaceholder
   * @returns Promise với job ID
   */
  async sendFeatureSuggestionThankYou(
    data: Partial<
      Record<
        keyof typeof FeatureSuggestionThankYouPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email cảm ơn đề xuất tính năng',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email cảm ơn đề xuất tính năng',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email cảm ơn đề xuất tính năng đến: ${data.EMAIL}`,
      );

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      // Tạo EmailSystemJobDto
      const emailData: EmailSystemJobDto = {
        category: CategoryTemplateAutoEnum.OTHER_THANK_FOR_FEATURE_SUGGESTION,
        to: cleanData.EMAIL,
        data: cleanData,
      };

      return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(
        emailData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email cảm ơn đề xuất tính năng: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email cảm ơn đề xuất tính năng',
          );
    }
  }

  /**
   * Gửi email thông báo giảm giá
   * @param data Object chứa các giá trị tương ứng với DiscountCampaignPlaceholder
   * @returns Promise với job ID
   */
  async sendDiscountCampaign(
    data: Partial<
      Record<
        keyof typeof DiscountCampaignPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo giảm giá',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo giảm giá',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email thông báo giảm giá đến: ${data.EMAIL}`,
      );

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      // Tạo EmailSystemJobDto
      const emailData: EmailSystemJobDto = {
        category: CategoryTemplateAutoEnum.MARKETING_MONTHLY_DISCOUNT,
        to: cleanData.EMAIL,
        data: cleanData,
      };

      return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(
        emailData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email thông báo giảm giá: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email thông báo giảm giá',
          );
    }
  }

  /**
   * Gửi email trợ lý chưa được bật quá 3 ngày
   * @param data Object chứa các giá trị tương ứng với AssistantInactivePlaceholder
   * @returns Promise với job ID
   */
  async sendAssistantInactive(
    data: Partial<
      Record<
        keyof typeof AssistantInactivePlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email trợ lý chưa được bật quá 3 ngày',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo trợ lý chưa được bật quá 3 ngày',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email trợ lý chưa được bật quá 3 ngày đến: ${data.EMAIL}`,
      );

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      // Tạo EmailSystemJobDto
      const emailData: EmailSystemJobDto = {
        category: CategoryTemplateAutoEnum.OPERATION_ASSISTANT_INACTIVE,
        to: cleanData.EMAIL,
        data: cleanData,
      };

      return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(
        emailData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email trợ lý chưa được bật quá 3 ngày: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email trợ lý chưa được bật quá 3 ngày',
          );
    }
  }

  /**
   * Gửi email đề xuất tính năng mới của nhân viên
   * @param data Object chứa các giá trị tương ứng với EmployeeFeatureSuggestionPlaceholder
   * @returns Promise với job ID
   */
  async sendEmployeeFeatureSuggestion(
    data: Partial<
      Record<
        keyof typeof EmployeeFeatureSuggestionPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email đề xuất tính năng mới của nhân viên',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo đề xuất tính năng mới của nhân viên',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email đề xuất tính năng mới của nhân viên đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'employee-feature-suggestion-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'EMPLOYEE_NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email đề xuất tính năng mới của nhân viên: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email đề xuất tính năng mới của nhân viên',
          );
    }
  }

  /**
   * Gửi email tích hợp cổng thanh toán
   * @param data Object chứa các giá trị tương ứng với PaymentGatewayIntegrationPlaceholder
   * @returns Promise với job ID
   */
  async sendPaymentGatewayIntegration(
    data: Partial<
      Record<
        keyof typeof PaymentGatewayIntegrationPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email tích hợp cổng thanh toán',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo tích hợp cổng thanh toán',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email tích hợp cổng thanh toán đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'payment-gateway-integration-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email tích hợp cổng thanh toán: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email tích hợp cổng thanh toán',
          );
    }
  }

  /**
   * Gửi email tích hợp website
   * @param data Object chứa các giá trị tương ứng với WebsiteIntegrationPlaceholder
   * @returns Promise với job ID
   */
  async sendWebsiteIntegration(
    data: Partial<
      Record<
        keyof typeof WebsiteIntegrationPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email tích hợp website',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi thông báo tích hợp website',
        );
      }

      this.logger.log(`Chuẩn bị gửi email tích hợp website đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'website-integration-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email tích hợp website: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email tích hợp website',
          );
    }
  }

  /**
   * Gửi email xin lỗi vì trải nghiệm tồi
   * @param data Object chứa các giá trị tương ứng với BadExperienceApologyPlaceholder
   * @returns Promise với job ID
   */
  async sendBadExperienceApology(
    data: Partial<
      Record<
        keyof typeof BadExperienceApologyPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email xin lỗi vì trải nghiệm tồi',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email xin lỗi vì trải nghiệm tồi',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email xin lỗi vì trải nghiệm tồi đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'bad-experience-apology-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email xin lỗi vì trải nghiệm tồi: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email xin lỗi vì trải nghiệm tồi',
          );
    }
  }

  /**
   * Gửi email nhắc ký hợp đồng nguyên tắc
   * @param data Object chứa các giá trị tương ứng với RuleContractReminderPlaceholder
   * @returns Promise với job ID
   */
  async sendRuleContractReminder(
    data: Partial<
      Record<
        keyof typeof RuleContractReminderPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email nhắc ký hợp đồng nguyên tắc',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email nhắc ký hợp đồng nguyên tắc',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email nhắc ký hợp đồng nguyên tắc đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'rule-contract-reminder-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email nhắc ký hợp đồng nguyên tắc: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email nhắc ký hợp đồng nguyên tắc',
          );
    }
  }

  /**
   * Gửi email quyền lợi ưu tiên đề xuất của đối tác
   * @param data Object chứa các giá trị tương ứng với AffiliatePriorityBenefitPlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliatePriorityBenefit(
    data: Partial<
      Record<
        keyof typeof AffiliatePriorityBenefitPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email quyền lợi ưu tiên đề xuất của đối tác',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email quyền lợi ưu tiên đề xuất của đối tác',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email quyền lợi ưu tiên đề xuất của đối tác đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-priority-benefit-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email quyền lợi ưu tiên đề xuất của đối tác: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email quyền lợi ưu tiên đề xuất của đối tác',
          );
    }
  }

  /**
   * Gửi email tặng function và chiến lược khi tăng Rank
   * @param data Object chứa các giá trị tương ứng với AffiliateRankUpRewardPlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliateRankUpReward(
    data: Partial<
      Record<
        keyof typeof AffiliateRankUpRewardPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email tặng function và chiến lược khi tăng Rank',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email tặng function và chiến lược khi tăng Rank',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email tặng function và chiến lược khi tăng Rank đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-rank-up-reward-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email tặng function và chiến lược khi tăng Rank: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email tặng function và chiến lược khi tăng Rank',
          );
    }
  }

  /**
   * Gửi email tích hợp Facebook
   * @param data Object chứa các giá trị tương ứng với FacebookIntegrationPlaceholder
   * @returns Promise với job ID
   */
  async sendFacebookIntegration(
    data: Partial<
      Record<
        keyof typeof FacebookIntegrationPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email tích hợp Facebook',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email tích hợp Facebook',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email tích hợp Facebook đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'facebook-integration-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email tích hợp Facebook: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email tích hợp Facebook',
          );
    }
  }

  /**
   * Gửi email hợp đồng nguyên tắc mới cần xử lý
   * @param data Object chứa các giá trị tương ứng với NewRuleContractProcessingPlaceholder
   * @returns Promise với job ID
   */
  async sendNewRuleContractProcessing(
    data: Partial<
      Record<
        keyof typeof NewRuleContractProcessingPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email hợp đồng nguyên tắc mới cần xử lý',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email hợp đồng nguyên tắc mới cần xử lý',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email hợp đồng nguyên tắc mới cần xử lý đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'new-rule-contract-processing-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'EMPLOYEE_NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email hợp đồng nguyên tắc mới cần xử lý: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email hợp đồng nguyên tắc mới cần xử lý',
          );
    }
  }

  /**
   * Gửi email sửa thông tin doanh nghiệp cần xử lý
   * @param data Object chứa các giá trị tương ứng với BusinessInfoUpdateProcessingPlaceholder
   * @returns Promise với job ID
   */
  async sendBusinessInfoUpdateProcessing(
    data: Partial<
      Record<
        keyof typeof BusinessInfoUpdateProcessingPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email sửa thông tin doanh nghiệp cần xử lý',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email sửa thông tin doanh nghiệp cần xử lý',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email sửa thông tin doanh nghiệp cần xử lý đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'business-info-update-processing-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'EMPLOYEE_NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email sửa thông tin doanh nghiệp cần xử lý: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email sửa thông tin doanh nghiệp cần xử lý',
          );
    }
  }

  /**
   * Gửi email ký hợp đồng nguyên tắc thất bại
   * @param data Object chứa các giá trị tương ứng với RuleContractSignFailedPlaceholder
   * @returns Promise với job ID
   */
  async sendRuleContractSignFailed(
    data: Partial<
      Record<
        keyof typeof RuleContractSignFailedPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email ký hợp đồng nguyên tắc thất bại',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email ký hợp đồng nguyên tắc thất bại',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email ký hợp đồng nguyên tắc thất bại đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'rule-contract-sign-failed-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email ký hợp đồng nguyên tắc thất bại: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email ký hợp đồng nguyên tắc thất bại',
          );
    }
  }

  /**
   * Gửi email đăng ký đối tác đang được xử lý
   * @param data Object chứa các giá trị tương ứng với AffiliateRegistrationProcessingPlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliateRegistrationProcessing(
    data: Partial<
      Record<
        keyof typeof AffiliateRegistrationProcessingPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email đăng ký đối tác đang được xử lý',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email đăng ký đối tác đang được xử lý',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email đăng ký đối tác đang được xử lý đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-registration-processing-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email đăng ký đối tác đang được xử lý: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email đăng ký đối tác đang được xử lý',
          );
    }
  }

  /**
   * Gửi email khiếu nại mới
   * @param data Object chứa các giá trị tương ứng với NewComplaintPlaceholder
   * @returns Promise với job ID
   */
  async sendNewComplaint(
    data: Partial<
      Record<keyof typeof NewComplaintPlaceholder, string | null | undefined>
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email khiếu nại mới',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email khiếu nại mới',
        );
      }

      this.logger.log(`Chuẩn bị gửi email khiếu nại mới đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'new-complaint-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'EMPLOYEE_NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email khiếu nại mới: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email khiếu nại mới',
          );
    }
  }

  /**
   * Gửi email hóa đơn mới cần xuất
   * @param data Object chứa các giá trị tương ứng với NewInvoiceProcessingPlaceholder
   * @returns Promise với job ID
   */
  async sendNewInvoiceProcessing(
    data: Partial<
      Record<
        keyof typeof NewInvoiceProcessingPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email hóa đơn mới cần xuất',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email hóa đơn mới cần xuất',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email hóa đơn mới cần xuất đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'new-invoice-processing-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'EMPLOYEE_NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email hóa đơn mới cần xuất: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email hóa đơn mới cần xuất',
          );
    }
  }

  /**
   * Gửi email ký hợp đồng cộng tác viên thành công
   * @param data Object chứa các giá trị tương ứng với AffiliateContractSignedPlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliateContractSigned(
    data: Partial<
      Record<
        keyof typeof AffiliateContractSignedPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email ký hợp đồng cộng tác viên thành công',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email ký hợp đồng cộng tác viên thành công',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email ký hợp đồng cộng tác viên thành công đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-contract-signed-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email ký hợp đồng cộng tác viên thành công: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email ký hợp đồng cộng tác viên thành công',
          );
    }
  }

  /**
   * Gửi email import khách hàng thành công
   * @param data Object chứa các giá trị tương ứng với CustomerImportSuccessPlaceholder
   * @returns Promise với job ID
   */
  async sendCustomerImportSuccess(
    data: Partial<
      Record<
        keyof typeof CustomerImportSuccessPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email import khách hàng thành công',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email import khách hàng thành công',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email import khách hàng thành công đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'customer-import-success-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email import khách hàng thành công: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email import khách hàng thành công',
          );
    }
  }

  /**
   * Gửi email đăng ký đối tác bị từ chối
   * @param data Object chứa các giá trị tương ứng với AffiliateRegistrationRejectedPlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliateRegistrationRejected(
    data: Partial<
      Record<
        keyof typeof AffiliateRegistrationRejectedPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email đăng ký đối tác bị từ chối',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email đăng ký đối tác bị từ chối',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email đăng ký đối tác bị từ chối đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-registration-rejected-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email đăng ký đối tác bị từ chối: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email đăng ký đối tác bị từ chối',
          );
    }
  }

  /**
   * Gửi email tặng mã giảm giá
   * @param data Object chứa các giá trị tương ứng với AffiliateDiscountCodePlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliateDiscountCode(
    data: Partial<
      Record<
        keyof typeof AffiliateDiscountCodePlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email tặng mã giảm giá',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email tặng mã giảm giá',
        );
      }

      this.logger.log(`Chuẩn bị gửi email tặng mã giảm giá đến: ${data.EMAIL}`);

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-discount-code-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email tặng mã giảm giá: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email tặng mã giảm giá',
          );
    }
  }

  /**
   * Gửi email yêu cầu rút tiền Affiliate đã được xử lý (Cá nhân)
   * @param data Object chứa các giá trị tương ứng với AffiliateWithdrawProcessedIndividualPlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliateWithdrawProcessedIndividual(
    data: Partial<
      Record<
        keyof typeof AffiliateWithdrawProcessedIndividualPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email yêu cầu rút tiền Affiliate đã được xử lý (Cá nhân)',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email yêu cầu rút tiền Affiliate đã được xử lý (Cá nhân)',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email yêu cầu rút tiền Affiliate đã được xử lý (Cá nhân) đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-withdraw-processed-individual-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email yêu cầu rút tiền Affiliate đã được xử lý (Cá nhân): ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email yêu cầu rút tiền Affiliate đã được xử lý (Cá nhân)',
          );
    }
  }

  /**
   * Gửi email yêu cầu rút tiền Affiliate đã được xử lý (Doanh nghiệp)
   * @param data Object chứa các giá trị tương ứng với AffiliateWithdrawProcessedBusinessPlaceholder
   * @returns Promise với job ID
   */
  async sendAffiliateWithdrawProcessedBusiness(
    data: Partial<
      Record<
        keyof typeof AffiliateWithdrawProcessedBusinessPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email yêu cầu rút tiền Affiliate đã được xử lý (Doanh nghiệp)',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email yêu cầu rút tiền Affiliate đã được xử lý (Doanh nghiệp)',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email yêu cầu rút tiền Affiliate đã được xử lý (Doanh nghiệp) đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'affiliate-withdraw-processed-business-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email yêu cầu rút tiền Affiliate đã được xử lý (Doanh nghiệp): ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email yêu cầu rút tiền Affiliate đã được xử lý (Doanh nghiệp)',
          );
    }
  }

  /**
   * Gửi email nâng cấp và bảo trì hệ thống
   * @param data Object chứa các giá trị tương ứng với SystemMaintenancePlaceholder
   * @returns Promise với job ID
   */
  async sendSystemMaintenance(
    data: Partial<
      Record<
        keyof typeof SystemMaintenancePlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email nâng cấp và bảo trì hệ thống',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email nâng cấp và bảo trì hệ thống',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email nâng cấp và bảo trì hệ thống đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'system-maintenance-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email nâng cấp và bảo trì hệ thống: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email nâng cấp và bảo trì hệ thống',
          );
    }
  }

  /**
   * Gửi email xóa cổng thanh toán
   * @param data Object chứa các giá trị tương ứng với PaymentGatewayDeletionPlaceholder
   * @returns Promise với job ID
   */
  async sendPaymentGatewayDeletion(
    data: Partial<
      Record<
        keyof typeof PaymentGatewayDeletionPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email xóa cổng thanh toán',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email xóa cổng thanh toán',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email xóa cổng thanh toán đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'payment-gateway-deletion-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email xóa cổng thanh toán: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email xóa cổng thanh toán',
          );
    }
  }

  /**
   * Gửi email kích hoạt lại tài khoản
   * @param data Object chứa các giá trị tương ứng với AccountReactivationPlaceholder
   * @returns Promise với job ID
   */
  async sendAccountReactivation(
    data: Partial<
      Record<
        keyof typeof AccountReactivationPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email kích hoạt lại tài khoản',
        );
      }

      // Kiểm tra mã xác thực có tồn tại không
      if (!data.TWO_FA_CODE) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu mã xác thực để gửi email kích hoạt lại tài khoản',
        );
      }

      this.logger.log(`Chuẩn bị gửi email kích hoạt lại tài khoản`);

      // ID của mẫu email trong hệ thống
      const templateId = 'account-reactivation-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['TWO_FA_CODE']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL, // Có thể null/undefined nếu gửi qua phương thức khác
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email kích hoạt lại tài khoản: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email kích hoạt lại tài khoản',
          );
    }
  }

  /**
   * Gửi email xác minh 2 lớp của nhân viên
   * @param data Object chứa các giá trị tương ứng với EmployeeTwoFAPlaceholder
   * @returns Promise với job ID
   */
  async sendEmployeeTwoFA(
    data: Partial<
      Record<keyof typeof EmployeeTwoFAPlaceholder, string | null | undefined>
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email xác minh 2 lớp của nhân viên',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email xác minh 2 lớp của nhân viên',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email xác minh 2 lớp của nhân viên đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'employee-two-fa-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, [
        'EMAIL',
        'EMPLOYEE_NAME',
        'TWO_FA_CODE',
      ]);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email xác minh 2 lớp của nhân viên: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email xác minh 2 lớp của nhân viên',
          );
    }
  }

  /**
   * Gửi email thông báo tài khoản nhân viên mới
   * @param data Object chứa các giá trị tương ứng với EmployeeAccountCreationPlaceholder
   * @returns Promise với job ID
   */
  async sendEmployeeAccountCreation(
    data: Partial<
      Record<keyof typeof EmployeeAccountCreationPlaceholder, string | null | undefined>
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email thông báo tài khoản nhân viên mới',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email thông báo tài khoản nhân viên mới',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email thông báo tài khoản nhân viên mới đến: ${data.EMAIL}`,
      );

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, [
        'EMAIL',
        'EMPLOYEE_NAME',
        'USERNAME',
        'PASSWORD',
      ]);

      // Tạo EmailSystemJobDto
      const emailData: EmailSystemJobDto = {
        category: CategoryTemplateAutoEnum.EMPLOYEE_OPERATION_ADMIN_LOGIN_INFO,
        to: cleanData.EMAIL,
        data: cleanData,
      };

      return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(
        emailData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email thông báo tài khoản nhân viên mới: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email thông báo tài khoản nhân viên mới',
          );
    }
  }

  /**
   * Phương thức để xác thực các trường dữ liệu bắt buộc
   * @param data Dữ liệu cần kiểm tra
   * @param requiredFields Danh sách các trường bắt buộc
   */
  private validateEssentialFields(
    data: Record<string, string>,
    requiredFields: string[],
  ): void {
    // Kiểm tra data có tồn tại không
    if (!data) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Dữ liệu email không được để trống',
      );
    }

    // Kiểm tra các trường bắt buộc
    const missingFields: string[] = [];

    for (const field of requiredFields) {
      if (!data[field] || data[field].trim() === '') {
        missingFields.push(field);
      }
    }

    if (missingFields.length > 0) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Thiếu các trường dữ liệu bắt buộc: ${missingFields.join(', ')}`,
      );
    }
  }

  /**
   * Phương thức chung để kiểm tra dữ liệu placeholder - không bắt buộc tất cả các trường
   * @param data Dữ liệu placeholder
   * @param enumType Loại enum placeholder
   */
  private validatePlaceholderData<T>(
    data: Record<string, string | null | undefined>,
    enumType: T,
  ): void {
    // Kiểm tra tham số data có null không
    if (!data) {
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Dữ liệu email không được để trống',
      );
    }

    // Kiểm tra các trường bắt buộc
    const requiredFields: string[] = [];

    Object.values(enumType as object).forEach((field) => {
      if (typeof field === 'string') {
        const value = data[field];
        // Xử lý cả null và undefined
        if (value === null || value === undefined || value === '') {
          requiredFields.push(field);
        }
      }
    });

    if (requiredFields.length > 0) {
      this.logger.warn(
        `Cảnh báo: Có các trường dữ liệu chưa được cung cấp: ${requiredFields.join(', ')}`,
      );
    }
  }

  /**
   * Phương thức để xử lý dữ liệu, loại bỏ các giá trị null và undefined
   * @param data Dữ liệu cần xử lý
   * @returns Dữ liệu đã được xử lý, không còn null/undefined
   */
  private sanitizeData<T extends Record<string, any>>(
    data: T,
  ): Record<string, string> {
    const result: Record<string, string> = {};

    if (!data) {
      return result;
    }

    // Duyệt qua tất cả các thuộc tính của đối tượng data
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = data[key];

        // Kiểm tra giá trị có phải null/undefined hay không
        if (value !== null && value !== undefined) {
          // Nếu là đối tượng hoặc mảng, chuyển đổi thành chuỗi JSON
          if (typeof value === 'object') {
            result[key] = JSON.stringify(value);
          }
          // Nếu là số hoặc boolean, chuyển đổi thành chuỗi
          else if (typeof value === 'number' || typeof value === 'boolean') {
            result[key] = String(value);
          }
          // Nếu là chuỗi, giữ nguyên
          else if (typeof value === 'string') {
            result[key] = value;
          }
          // Các trường hợp khác, chuyển đổi thành chuỗi
          else {
            result[key] = String(value);
          }
        } else {
          // Thay các giá trị null/undefined bằng chuỗi rỗng
          result[key] = '';
        }
      }
    }

    return result;
  }

  /**
   * Gửi email ký hợp đồng nguyên tắc thành công
   * @param data Object chứa các giá trị tương ứng với RuleContractSignedPlaceholder
   * @returns Promise với job ID
   */
  async sendRuleContractSigned(
    data: Partial<
      Record<
        keyof typeof RuleContractSignedPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email ký hợp đồng nguyên tắc thành công',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email ký hợp đồng nguyên tắc thành công',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email ký hợp đồng nguyên tắc thành công đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'rule-contract-signed-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email ký hợp đồng nguyên tắc thành công: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email ký hợp đồng nguyên tắc thành công',
          );
    }
  }

  /**
   * Gửi email hợp đồng nguyên tắc đang được xử lý
   * @param data Object chứa các giá trị tương ứng với RuleContractProcessingPlaceholder
   * @returns Promise với job ID
   */
  async sendRuleContractProcessing(
    data: Partial<
      Record<
        keyof typeof RuleContractProcessingPlaceholder,
        string | null | undefined
      >
    >,
  ): Promise<string | undefined> {
    try {
      // Kiểm tra data có tồn tại không
      if (!data) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Không có dữ liệu để gửi email hợp đồng nguyên tắc đang được xử lý',
        );
      }

      // Kiểm tra email có tồn tại không
      if (!data.EMAIL) {
        throw new AppException(
          ErrorCode.INTERNAL_SERVER_ERROR,
          'Thiếu địa chỉ email để gửi email hợp đồng nguyên tắc đang được xử lý',
        );
      }

      this.logger.log(
        `Chuẩn bị gửi email hợp đồng nguyên tắc đang được xử lý đến: ${data.EMAIL}`,
      );

      // ID của mẫu email trong hệ thống
      const templateId = 'rule-contract-processing-template';

      // Khởi tạo đối tượng dữ liệu sạch không chứa null/undefined
      const cleanData = this.sanitizeData(data as Record<string, any>);

      // Kiểm tra trường bắt buộc
      this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

      return await this.emailService.sendTemplateEmail(
        cleanData.EMAIL,
        templateId,
        cleanData,
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi gửi email hợp đồng nguyên tắc đang được xử lý: ${error.message}`,
        error.stack,
      );
      throw error instanceof AppException
        ? error
        : new AppException(
            ErrorCode.INTERNAL_SERVER_ERROR,
            'Không thể gửi email hợp đồng nguyên tắc đang được xử lý',
          );
    }
  }
}
