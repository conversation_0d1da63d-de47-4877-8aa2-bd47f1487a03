import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

/**
 * DTO cho việc kết nối tài khoản WhatsApp với agent
 */
export class ConnectWhatsAppAgentDto {
  @ApiProperty({
    description: 'ID agent được kết nối với tài khoản WhatsApp',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty()
  @IsString()
  @IsUUID()
  agentId: string;
}
