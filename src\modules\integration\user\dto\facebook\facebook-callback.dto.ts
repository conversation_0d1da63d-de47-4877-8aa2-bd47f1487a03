import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc xử lý callback từ Facebook
 */
export class FacebookCallbackDto {
  @ApiProperty({
    description: 'Mã xác thực từ Facebook',
    example: 'AQD1_ELlQVmTDM0I...'
  })
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Endpoint callback sau khi xác thực',
    example: '/callback'
  })
  @IsNotEmpty()
  @IsString()
  redirectUri: string;

  @ApiProperty({
    description: 'State token để xác thực callback',
    example: '123'
  })
  @IsNotEmpty()
  @IsString()
  state: string;
}
