# API Xóa Nhiều Công Việc

## Endpoint
```
DELETE /api/todos/bulk
```

## Request Body
```json
{
  "ids": [1, 2, 3, 4, 5]
}
```

## Response
```json
{
  "success": true,
  "message": "<PERSON><PERSON><PERSON> nhiều công việc hoàn tất",
  "data": {
    "totalRequested": 5,
    "successCount": 3,
    "failureCount": 2,
    "deletedIds": [1, 3, 5],
    "failures": [
      {
        "id": 2,
        "reason": "Không tìm thấy công việc"
      },
      {
        "id": 4,
        "reason": "Bạn không có quyền xóa công việc này"
      }
    ]
  }
}
```

## Validation
- `ids` phải là mảng số nguyên
- Phải có ít nhất 1 ID trong mảng
- Mỗi ID phải là số hợp lệ

## Business Logic
- Chỉ người tạo công việc mới có quyền xóa
- Công việc không tồn tại sẽ được báo lỗi
- Trả về kết quả chi tiết cho từng công việc

## Curl Example
```bash
curl -X DELETE http://localhost:3000/api/todos/bulk \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"ids": [1, 2, 3]}'
```
