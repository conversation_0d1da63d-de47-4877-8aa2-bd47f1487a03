import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response/api-response-dto';
import { DocumentService } from '../services/document.service';
import {
  CreateUploadUrlDto,
  UploadUrlResponseDto,
  ConfirmUploadDto,
  DocumentResponseDto,
  DocumentWithDownloadUrlResponseDto,
  DocumentQueryDto,
  DocumentSearchDto,
  UpdateDocumentDto,
} from '../dto/document';

/**
 * Controller xử lý các API cho tài liệu
 */
@ApiTags('Documents')
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  UploadUrlResponseDto,
  DocumentResponseDto,
  DocumentWithDownloadUrlResponseDto,
)
@Controller('documents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class DocumentController {
  private readonly logger = new Logger(DocumentController.name);

  constructor(private readonly documentService: DocumentService) {}

  /**
   * Tạo presigned URL để upload tài liệu
   */
  @Post('upload-url')
  @ApiOperation({
    summary: 'Tạo presigned URL để upload tài liệu',
    description: 'Tạo URL tạm thời có chữ ký để frontend upload file trực tiếp lên S3',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo presigned URL thành công',
    schema: ApiResponseDto.getSchema(UploadUrlResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Thư mục không tồn tại',
  })
  async createUploadUrl(
    @Body() createUploadDto: CreateUploadUrlDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UploadUrlResponseDto>> {
    this.logger.log(
      `Tạo presigned URL cho file: ${createUploadDto.fileName} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentService.createUploadUrl(
      Number(user.tenantId),
      createUploadDto,
      user.id,
    );

    return ApiResponseDto.created(result, 'Tạo presigned URL thành công');
  }

  /**
   * Xác nhận upload thành công
   */
  @Post('confirm-upload')
  @ApiOperation({
    summary: 'Xác nhận upload thành công',
    description: 'Xác nhận file đã được upload thành công và tạo document record',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Xác nhận upload thành công',
    schema: ApiResponseDto.getSchema(DocumentResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Upload ID không hợp lệ hoặc đã hết hạn',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'File không tồn tại trên S3',
  })
  async confirmUpload(
    @Body() confirmDto: ConfirmUploadDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DocumentResponseDto>> {
    this.logger.log(
      `Xác nhận upload với ID: ${confirmDto.uploadId} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentService.confirmUpload(
      Number(user.tenantId),
      confirmDto,
      user.id,
    );

    return ApiResponseDto.created(result, 'Xác nhận upload thành công');
  }

  /**
   * Lấy danh sách tài liệu
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách tài liệu',
    description: 'Lấy danh sách tài liệu với phân trang và bộ lọc',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (mặc định: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng mỗi trang (mặc định: 10)' })
  @ApiQuery({ name: 'search', required: false, description: 'Từ khóa tìm kiếm' })
  @ApiQuery({ name: 'documentType', required: false, description: 'Loại tài liệu' })
  @ApiQuery({ name: 'folderId', required: false, description: 'ID thư mục' })
  @ApiQuery({ name: 'isActive', required: false, description: 'Trạng thái hoạt động' })
  @ApiQuery({ name: 'isPublic', required: false, description: 'Tài liệu công khai' })
  @ApiQuery({ name: 'processingStatus', required: false, description: 'Trạng thái xử lý' })
  @ApiQuery({ name: 'tags', required: false, description: 'Tags (phân cách bằng dấu phẩy)' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Trường sắp xếp' })
  @ApiQuery({ name: 'sortDirection', required: false, description: 'Hướng sắp xếp (ASC/DESC)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách tài liệu thành công',
    schema: ApiResponseDto.getPaginatedSchema(DocumentResponseDto),
  })
  async findAll(
    @Query() queryDto: DocumentQueryDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<DocumentResponseDto>>> {
    this.logger.log(
      `Lấy danh sách tài liệu (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentService.findAll(
      Number(user.tenantId),
      queryDto,
    );

    return ApiResponseDto.paginated(result, 'Lấy danh sách tài liệu thành công');
  }

  /**
   * Tìm kiếm full-text
   */
  @Get('search')
  @ApiOperation({
    summary: 'Tìm kiếm full-text tài liệu',
    description: 'Tìm kiếm tài liệu theo nội dung văn bản',
  })
  @ApiQuery({ name: 'searchText', required: true, description: 'Từ khóa tìm kiếm' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (mặc định: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng mỗi trang (mặc định: 10)' })
  @ApiQuery({ name: 'documentType', required: false, description: 'Loại tài liệu' })
  @ApiQuery({ name: 'folderId', required: false, description: 'ID thư mục' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tìm kiếm thành công',
    schema: ApiResponseDto.getPaginatedSchema(DocumentResponseDto),
  })
  async searchFullText(
    @Query() searchDto: DocumentSearchDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<DocumentResponseDto>>> {
    this.logger.log(
      `Tìm kiếm full-text: "${searchDto.searchText}" (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentService.searchFullText(
      Number(user.tenantId),
      searchDto,
      user.id,
    );

    return ApiResponseDto.paginated(result, 'Tìm kiếm thành công');
  }

  /**
   * Lấy chi tiết tài liệu
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết tài liệu',
    description: 'Lấy thông tin chi tiết của một tài liệu',
  })
  @ApiParam({ name: 'id', description: 'ID tài liệu' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy chi tiết tài liệu thành công',
    schema: ApiResponseDto.getSchema(DocumentResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài liệu',
  })
  async findById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DocumentResponseDto | null>> {
    this.logger.log(
      `Lấy chi tiết tài liệu ID: ${id} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentService.findById(
      Number(user.tenantId),
      id,
      user.id,
    );

    if (!result) {
      return ApiResponseDto.success(null, 'Không tìm thấy tài liệu');
    }

    return ApiResponseDto.success(result, 'Lấy chi tiết tài liệu thành công');
  }

  /**
   * Lấy URL download cho tài liệu
   */
  @Get(':id/download-url')
  @ApiOperation({
    summary: 'Lấy URL download cho tài liệu',
    description: 'Tạo URL tạm thời có chữ ký để download tài liệu',
  })
  @ApiParam({ name: 'id', description: 'ID tài liệu' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tạo URL download thành công',
    schema: ApiResponseDto.getSchema(DocumentWithDownloadUrlResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài liệu',
  })
  async getDownloadUrl(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DocumentWithDownloadUrlResponseDto>> {
    this.logger.log(
      `Tạo URL download cho tài liệu ID: ${id} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentService.getDownloadUrl(
      Number(user.tenantId),
      id,
      user.id,
    );

    return ApiResponseDto.success(result, 'Tạo URL download thành công');
  }

  /**
   * Cập nhật tài liệu
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật tài liệu',
    description: 'Cập nhật thông tin metadata của tài liệu',
  })
  @ApiParam({ name: 'id', description: 'ID tài liệu' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật tài liệu thành công',
    schema: ApiResponseDto.getSchema(DocumentResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài liệu',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateDocumentDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<DocumentResponseDto | null>> {
    this.logger.log(
      `Cập nhật tài liệu ID: ${id} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentService.update(
      Number(user.tenantId),
      id,
      updateDto,
      user.id,
    );

    if (!result) {
      return ApiResponseDto.success(null, 'Không tìm thấy tài liệu');
    }

    return ApiResponseDto.updated(result, 'Cập nhật tài liệu thành công');
  }

  /**
   * Xóa tài liệu
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa tài liệu',
    description: 'Xóa tài liệu (soft delete)',
  })
  @ApiParam({ name: 'id', description: 'ID tài liệu' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa tài liệu thành công',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy tài liệu',
  })
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<boolean>> {
    this.logger.log(
      `Xóa tài liệu ID: ${id} (user: ${user.id}, tenant: ${user.tenantId})`,
    );

    const result = await this.documentService.delete(
      Number(user.tenantId),
      id,
      user.id,
    );

    if (!result) {
      return ApiResponseDto.success(false, 'Không tìm thấy tài liệu');
    }

    return ApiResponseDto.deleted(true, 'Xóa tài liệu thành công');
  }
}
