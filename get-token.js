const axios = require('axios');

async function getToken() {
  try {
    console.log('Getting token...');
    
    const loginResponse = await axios.post('http://localhost:3001/v1/auth/user/login', {
      email: '<EMAIL>',
      password: 'password123'
    });

    console.log('Login Response:', JSON.stringify(loginResponse.data, null, 2));
    
    if (loginResponse.data.result && loginResponse.data.result.accessToken) {
      console.log('\nToken:', loginResponse.data.result.accessToken);
      return loginResponse.data.result.accessToken;
    } else {
      console.log('No token found in response');
    }
  } catch (error) {
    console.error('Login Error:', error.response?.data || error.message);
  }
}

getToken();
