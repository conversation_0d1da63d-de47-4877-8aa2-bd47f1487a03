import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength, MinLength } from 'class-validator';

/**
 * DTO cho việc tạo mới tài khoản liên kết ngân hàng ACB dành cho cá nhân
 */
export class CreateAcbBankAccountDto {
  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  accountHolderName: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(5)
  @MaxLength(30)
  accountNumber: string;

  @ApiProperty({
    description: 'Tên gợi nhớ',
    example: 'Tài khoản ACB của tôi'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  label: string;
}
