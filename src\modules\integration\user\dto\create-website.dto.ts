import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUrl, MaxLength, IsOptional, IsIn } from 'class-validator';

/**
 * DTO cho việc tạo mới website
 */
export class CreateWebsiteDto {
  @ApiProperty({
    description: 'Tên website do người dùng đặt',
    example: 'Website của tôi',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên website không được để trống' })
  @IsString({ message: 'Tên website phải là chuỗi' })
  @MaxLength(255, { message: 'Tên website không được vượt quá 255 ký tự' })
  websiteName: string;

  @ApiProperty({
    description: 'Tên miền hoặc địa chỉ host của website',
    example: 'example.com',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Host không được để trống' })
  @IsString({ message: 'Host phải là chuỗi' })
  @IsUrl({}, { message: 'Host phải là URL hợp lệ' })
  @MaxLength(255, { message: 'Host không được vượt quá 255 ký tự' })
  host: string;

  @ApiProperty({
    description: 'MIME type của logo (để tạo presigned URL upload)',
    example: 'image/png',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Logo MIME type phải là chuỗi' })
  @IsIn(['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'], {
    message: 'Logo MIME type phải là một trong: image/png, image/jpeg, image/jpg, image/gif, image/webp'
  })
  logoMime?: string;
}
