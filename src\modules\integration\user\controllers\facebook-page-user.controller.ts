import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAG, SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { Body, Controller, Delete, Get, HttpStatus, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { INTEGRATION_ERROR_CODES } from '../../exceptions/integration-error.code';
import {
  FacebookAuthResponseDto
} from '../dto';
import {
  CallbackResponseDto,
  DeleteFacebookPagesDto,
  FacebookPageResponseDto
} from '../dto/facebook';
import { CreateFacebookUrlAuthDto } from '../dto/facebook/create-url-auth.dto';
import { FacebookCallbackDto } from '../dto/facebook/facebook-callback.dto';
import { FacebookPageQueryDto } from '../dto/facebook/facebook-page-query.dto';
import { FacebookPageUserService } from '../services/facebook-page-user.service';

@ApiTags(SWAGGER_API_TAG.USER_INTEGRATION_FACEBOOK)
@Controller('integration/facebook')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class FacebookPageUserController {
  constructor(
    private readonly facebookPageUserService: FacebookPageUserService,
  ) { }

  /**
   * Lấy danh sách trang Facebook với phân trang
   */
  @Get('pages')
  @ApiOperation({ summary: 'Lấy danh sách trang Facebook với phân trang' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách trang Facebook thành công',
  })
  async findFacebookPages(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: FacebookPageQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<FacebookPageResponseDto>>> {
    const result = await this.facebookPageUserService.findFacebookPages(user.id, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách trang Facebook thành công');
  }

  /**
   * Tạo URL xác thực Facebook
   */
  @Get('auth/url')
  @ApiOperation({ summary: 'Tạo URL xác thực Facebook' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tạo URL xác thực Facebook thành công',
  })
  async createFacebookAuthUrl(
    @CurrentUser() user: JwtPayload,
    @Query() createUrlAuthDto: CreateFacebookUrlAuthDto
  ): Promise<ApiResponseDto<FacebookAuthResponseDto>> {
    const authUrl = await this.facebookPageUserService.createFacebookAuthUrl(user.id, createUrlAuthDto.redirectUrl);
    return ApiResponseDto.success(authUrl, 'Tạo URL xác thực Facebook thành công');
  }

  /**
   * Xử lý callback từ Facebook
   */
  @Post('auth/callback')
  @ApiOperation({ summary: 'Xử lý callback từ Facebook' })
  @ApiBody({ type: FacebookCallbackDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xử lý callback từ Facebook thành công',
  })
  async handleFacebookCallback(
    @Body() callbackDto: FacebookCallbackDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<CallbackResponseDto>> {
    const personal = await this.facebookPageUserService.handleFacebookCallback(user.id, callbackDto.code, callbackDto.redirectUri, callbackDto.state);
    return ApiResponseDto.success(personal, 'Xử lý callback từ Facebook thành công');
  }

  /**
   * Xóa mềm một trang Facebook
   */
  @Delete('pages/:pageId')
  @ApiOperation({ summary: 'Xóa mềm một trang Facebook' })
  @ApiParam({ name: 'pageId', description: 'ID của trang Facebook cần xóa' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa mềm trang Facebook thành công',
  })
  @ApiErrorResponse(
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_NOT_FOUND,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_DELETE_FAILED,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_UNSUBSCRIBE_FAILED
  )
  async deleteFacebookPage(
    @Param('pageId') pageId: string,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<null>> {
    await this.facebookPageUserService.deleteFacebookPage(user.id, pageId);
    return ApiResponseDto.success(null, 'Xóa mềm trang Facebook thành công');
  }

  /**
   * Xóa mềm nhiều trang Facebook cùng lúc
   */
  @Delete('pages')
  @ApiOperation({ summary: 'Xóa mềm nhiều trang Facebook cùng lúc' })
  @ApiBody({ type: DeleteFacebookPagesDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa mềm các trang Facebook thành công',
    schema: {
      properties: {
        deletedCount: { type: 'number', example: 2 },
        errorPages: { type: 'array', items: { type: 'string' }, example: ['Trang A', 'Trang B'] }
      }
    }
  })
  @ApiErrorResponse(
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_DELETE_FAILED,
    INTEGRATION_ERROR_CODES.FACEBOOK_PAGE_UNSUBSCRIBE_FAILED
  )
  async deleteManyFacebookPages(
    @Body() deleteDto: DeleteFacebookPagesDto,
    @CurrentUser() user: JwtPayload
  ): Promise<ApiResponseDto<{ deletedCount: number; errorPages?: string[] }>> {
    const result = await this.facebookPageUserService.deleteManyFacebookPages(user.id, deleteDto.pageIds);
    return ApiResponseDto.success(result, 'Xóa mềm các trang Facebook thành công');
  }
}
