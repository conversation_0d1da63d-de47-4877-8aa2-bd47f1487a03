import { ApiProperty } from '@nestjs/swagger';
import { FacebookPageResponseDto } from './facebook-page-response.dto';

/**
 * DTO cho response của tài khoản Facebook cá nhân
 */
export class FacebookPersonalResponseDto {
  /**
   * ID của tài khoản Facebook cá nhân
   */
  @ApiProperty({
    description: 'ID của tài khoản Facebook cá nhân',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  id: string;

  /**
   * ID người dùng
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1
  })
  userId: number;

  /**
   * ID tài khoản Facebook cá nhân
   */
  @ApiProperty({
    description: 'ID tài khoản Facebook cá nhân',
    example: '123456789012345'
  })
  facebookPersonalId: string;

  /**
   * <PERSON><PERSON><PERSON> hết hạn của token
   */
  @ApiProperty({
    description: '<PERSON><PERSON>y hết hạn của token',
    example: '2023-05-01T00:00:00.000Z',
    nullable: true
  })
  expirationDate: Date | null;

  /**
   * Thời gian hết hạn của token (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian hết hạn của token (Unix timestamp)',
    example: 1682899200000,
    nullable: true
  })
  expirationDateUnix: number | null;

  /**
   * Danh sách trang Facebook của tài khoản cá nhân
   */
  @ApiProperty({
    description: 'Danh sách trang Facebook của tài khoản cá nhân',
    type: [FacebookPageResponseDto]
  })
  facebookPages: FacebookPageResponseDto[];
}
