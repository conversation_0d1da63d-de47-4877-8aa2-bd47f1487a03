import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto } from '@common/dto';

/**
 * DTO cho việc truy vấn danh sách cấu hình máy chủ email của người dùng với phân trang
 */
export class EmailServerQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên máy chủ hoặc host',
    required: false,
    example: 'gmail',
  })
  @IsOptional()
  @IsString()
  search: string = '';

  @ApiProperty({
    description: 'Lọc theo trạng thái hoạt động',
    required: false,
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  isActive?: boolean;
}
