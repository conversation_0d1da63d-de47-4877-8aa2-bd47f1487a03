import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { QueryDto } from '@common/dto';
import { ProviderShipmentType } from '@/modules/integration/constants/provider-shipment-type.enum';

/**
 * DTO cho query danh sách cấu hình nhà cung cấp vận chuyển
 */
export class ShipmentProviderQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo loại nhà cung cấp',
    enum: ProviderShipmentType,
    required: false
  })
  @IsOptional()
  @IsEnum(ProviderShipmentType)
  type?: ProviderShipmentType;
}
