import { ApiProperty } from '@nestjs/swagger';
import { ObjectiveType } from '../../enum/objective-type.enum';
import { OkrCycleStatus } from '../../enum/okr-cycle-status.enum';

/**
 * DTO for objective response
 */
export class ObjectiveResponseDto {
  /**
   * Unique identifier for the objective
   * @example 1
   */
  @ApiProperty({
    description: 'ID của mục tiêu',
    example: 1,
  })
  id: number;

  /**
   * Title of the objective
   * @example "Tăng doanh thu 20%"
   */
  @ApiProperty({
    description: 'Tiêu đề mục tiêu',
    example: 'Tăng doanh thu 20%',
  })
  title: string;

  /**
   * Detailed description of the objective
   * @example "Tăng doanh thu 20% so với quý trước thông qua các chiến dịch marketing mới"
   */
  @ApiProperty({
    description: '<PERSON>ô tả chi tiết mục tiêu',
    example:
      'Tăng doanh thu 20% so với quý trước thông qua các chiến dịch marketing mới',
    nullable: true,
  })
  description: string | null;

  /**
   * ID of the user responsible for the objective
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người chịu trách nhiệm',
    example: 1,
  })
  ownerId: number;

  /**
   * ID of the department (if applicable)
   * @example 2
   */
  @ApiProperty({
    description: 'ID của phòng ban (nếu có)',
    example: 2,
    nullable: true,
  })
  departmentId: number | null;



  /**
   * ID of the OKR cycle
   * @example 1
   */
  @ApiProperty({
    description: 'ID của chu kỳ OKR',
    example: 1,
  })
  cycleId: number | null;

  /**
   * OKR cycle information
   */
  @ApiProperty({
    description: 'Thông tin chu kỳ OKR',
    type: 'object',
    properties: {
      id: { type: 'number', example: 1 },
      name: { type: 'string', example: 'Q1-2025' },
      startDate: { type: 'string', format: 'date', example: '2025-01-01' },
      endDate: { type: 'string', format: 'date', example: '2025-03-31' },
      status: { enum: ['active', 'closed', 'planning'], example: 'active' },
    },
    nullable: true,
  })
  cycle: {
    id: number;
    name: string;
    startDate: string;
    endDate: string;
    status: OkrCycleStatus | null;
  } | null;

  /**
   * Type of the objective
   * @example "COMPANY"
   */
  @ApiProperty({
    description: 'Loại mục tiêu',
    enum: ObjectiveType,
    example: ObjectiveType.COMPANY,
  })
  type: ObjectiveType;

  /**
   * Objective completion progress (percentage)
   * @example 75
   */
  @ApiProperty({
    description: 'Tiến độ hoàn thành mục tiêu (phần trăm)',
    example: 75,
    nullable: true,
  })
  progress: number | null;

  /**
   * Status of the objective
   * @example "active"
   */
  @ApiProperty({
    description: 'Trạng thái mục tiêu',
    example: 'active',
    nullable: true,
  })
  status: string | null;



  /**
   * ID of the user who created the objective
   * @example 1
   */
  @ApiProperty({
    description: 'ID của người tạo mục tiêu',
    example: 1,
  })
  createdBy: number;

  /**
   * Creation timestamp (in milliseconds)
   * @example 1672531200000
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1672531200000,
    nullable: true,
  })
  createdAt: number | null;

  /**
   * Last update timestamp (in milliseconds)
   * @example 1672617600000
   */
  @ApiProperty({
    description: 'Thời gian cập nhật gần nhất (timestamp)',
    example: 1672617600000,
    nullable: true,
  })
  updatedAt: number | null;
}
