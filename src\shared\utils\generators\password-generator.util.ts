/**
 * Utility để tạo mật khẩu ngẫu nhiên
 */

/**
 * Tạ<PERSON> mật khẩu ngẫu nhiên đáp ứng yêu cầu bảo mật
 * @param length Độ dài mật khẩu (mặc định 12)
 * @returns Mật khẩu ngẫu nhiên có chứa chữ hoa, chữ thường, số và ký tự đặc biệt
 * @example generateRandomPassword(12) => "Abc123@Def45"
 */
export const generateRandomPassword = (length: number = 12): string => {
  // Định nghĩa các ký tự có thể sử dụng
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const specialChars = '@$!%*?&';

  // Đảm bảo mật khẩu có ít nhất 1 ký tự từ mỗi loại
  let password = '';
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += specialChars[Math.floor(Math.random() * specialChars.length)];

  // Tạo các ký tự còn lại ngẫu nhiên
  const allChars = lowercase + uppercase + numbers + specialChars;
  for (let i = password.length; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // Trộn các ký tự để tránh pattern cố định
  return password
    .split('')
    .sort(() => Math.random() - 0.5)
    .join('');
};

/**
 * Tạo username từ email
 * @param email Địa chỉ email
 * @returns Username được tạo từ email với số ngẫu nhiên
 * @example generateUsernameFromEmail("<EMAIL>") => "johndoe123"
 */
export const generateUsernameFromEmail = (email: string): string => {
  // Lấy phần trước @ và loại bỏ ký tự đặc biệt
  const emailParts = email.split('@');
  const usernameBase = emailParts[0].toLowerCase().replace(/[^a-z0-9]/g, '');
  
  // Thêm 3 số ngẫu nhiên
  const randomNumbers = Math.floor(Math.random() * 900) + 100; // 100-999
  
  return `${usernameBase}${randomNumbers}`;
};
