import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin phân bố nhân viên theo phòng ban
 */
export class DepartmentDistributionItemDto {
  /**
   * ID phòng ban
   * @example 1
   */
  @ApiProperty({
    description: 'ID phòng ban',
    example: 1,
    nullable: true,
  })
  departmentId: number | null;

  /**
   * Tên phòng ban
   * @example "Công nghệ thông tin"
   */
  @ApiProperty({
    description: 'Tên phòng ban',
    example: 'Công nghệ thông tin',
  })
  departmentName: string;

  /**
   * Số lượng nhân viên trong phòng ban
   * @example 45
   */
  @ApiProperty({
    description: 'Số lượng nhân viên trong phòng ban',
    example: 45,
  })
  employeeCount: number;

  /**
   * Tỷ lệ phần trăm so với tổng số nhân viên
   * @example 36.0
   */
  @ApiProperty({
    description: 'Tỷ lệ phần trăm so với tổng số nhân viên',
    example: 36.0,
  })
  percentage: number;
}

/**
 * DTO cho phản hồi phân bố nhân viên theo phòng ban
 */
export class EmployeeDepartmentDistributionResponseDto {
  /**
   * Tổng số nhân viên
   * @example 125
   */
  @ApiProperty({
    description: 'Tổng số nhân viên',
    example: 125,
  })
  totalEmployees: number;

  /**
   * Danh sách phân bố theo phòng ban
   */
  @ApiProperty({
    description: 'Danh sách phân bố nhân viên theo phòng ban',
    type: [DepartmentDistributionItemDto],
  })
  departments: DepartmentDistributionItemDto[];

  /**
   * Số lượng nhân viên chưa được phân phòng ban
   * @example 5
   */
  @ApiProperty({
    description: 'Số lượng nhân viên chưa được phân phòng ban',
    example: 5,
  })
  unassignedEmployees: number;

  /**
   * Tỷ lệ phần trăm nhân viên chưa được phân phòng ban
   * @example 4.0
   */
  @ApiProperty({
    description: 'Tỷ lệ phần trăm nhân viên chưa được phân phòng ban',
    example: 4.0,
  })
  unassignedPercentage: number;
}
