/**
 * Error codes cho Integration module
 */
export const INTEGRATION_ERROR_CODES = {
  // General errors
  INVALID_DATA: 'INVALID_DATA',
  ENCRYPTION_FAILED: 'ENCRYPTION_FAILED',
  DECRYPTION_FAILED: 'DECRYPTION_FAILED',
  
  // Provider errors
  PROVIDER_NOT_FOUND: 'PROVIDER_NOT_FOUND',
  UNSUPPORTED_PROVIDER: 'UNSUPPORTED_PROVIDER',
  PROVIDER_CONFIG_INVALID: 'PROVIDER_CONFIG_INVALID',
  
  // Connection test errors
  CONNECTION_TEST_FAILED: 'CONNECTION_TEST_FAILED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  PROVIDER_UNAVAILABLE: 'PROVIDER_UNAVAILABLE',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  
  // GHN specific errors
  GHN_INVALID_TOKEN: 'GHN_INVALID_TOKEN',
  G<PERSON><PERSON>_SHOP_NOT_FOUND: 'GHN_SHOP_NOT_FOUND',
  GHN_API_ERROR: 'GHN_API_ERROR',
  
  // GHTK specific errors
  GHTK_INVALID_TOKEN: 'GHTK_INVALID_TOKEN',
  GHTK_INVALID_PARTNER_CODE: 'GHTK_INVALID_PARTNER_CODE',
  GHTK_API_ERROR: 'GHTK_API_ERROR',
  
  // Ahamove specific errors
  AHAMOVE_INVALID_API_KEY: 'AHAMOVE_INVALID_API_KEY',
  AHAMOVE_INVALID_MOBILE: 'AHAMOVE_INVALID_MOBILE',
  AHAMOVE_AUTH_FAILED: 'AHAMOVE_AUTH_FAILED',
  AHAMOVE_API_ERROR: 'AHAMOVE_API_ERROR',
  
  // J&T specific errors
  JT_INVALID_USERNAME: 'JT_INVALID_USERNAME',
  JT_INVALID_API_KEY: 'JT_INVALID_API_KEY',
  JT_SIGNATURE_ERROR: 'JT_SIGNATURE_ERROR',
  JT_API_ERROR: 'JT_API_ERROR',
  
  // Database errors
  USER_PROVIDER_NOT_FOUND: 'USER_PROVIDER_NOT_FOUND',
  USER_PROVIDER_CREATE_FAILED: 'USER_PROVIDER_CREATE_FAILED',
  USER_PROVIDER_UPDATE_FAILED: 'USER_PROVIDER_UPDATE_FAILED',
  USER_PROVIDER_DELETE_FAILED: 'USER_PROVIDER_DELETE_FAILED',
} as const;

/**
 * Error messages tương ứng với error codes
 */
export const INTEGRATION_ERROR_MESSAGES = {
  [INTEGRATION_ERROR_CODES.INVALID_DATA]: 'Dữ liệu không hợp lệ',
  [INTEGRATION_ERROR_CODES.ENCRYPTION_FAILED]: 'Lỗi mã hóa dữ liệu',
  [INTEGRATION_ERROR_CODES.DECRYPTION_FAILED]: 'Lỗi giải mã dữ liệu',
  
  [INTEGRATION_ERROR_CODES.PROVIDER_NOT_FOUND]: 'Không tìm thấy nhà cung cấp',
  [INTEGRATION_ERROR_CODES.UNSUPPORTED_PROVIDER]: 'Nhà cung cấp không được hỗ trợ',
  [INTEGRATION_ERROR_CODES.PROVIDER_CONFIG_INVALID]: 'Cấu hình nhà cung cấp không hợp lệ',
  
  [INTEGRATION_ERROR_CODES.CONNECTION_TEST_FAILED]: 'Kiểm tra kết nối thất bại',
  [INTEGRATION_ERROR_CODES.INVALID_CREDENTIALS]: 'Thông tin xác thực không hợp lệ',
  [INTEGRATION_ERROR_CODES.PROVIDER_UNAVAILABLE]: 'Nhà cung cấp không khả dụng',
  [INTEGRATION_ERROR_CODES.TIMEOUT_ERROR]: 'Hết thời gian chờ kết nối',
  [INTEGRATION_ERROR_CODES.NETWORK_ERROR]: 'Lỗi kết nối mạng',
  
  // GHN
  [INTEGRATION_ERROR_CODES.GHN_INVALID_TOKEN]: 'Token GHN không hợp lệ',
  [INTEGRATION_ERROR_CODES.GHN_SHOP_NOT_FOUND]: 'Không tìm thấy Shop ID trong tài khoản GHN',
  [INTEGRATION_ERROR_CODES.GHN_API_ERROR]: 'Lỗi API GHN',
  
  // GHTK
  [INTEGRATION_ERROR_CODES.GHTK_INVALID_TOKEN]: 'Token GHTK không hợp lệ',
  [INTEGRATION_ERROR_CODES.GHTK_INVALID_PARTNER_CODE]: 'Mã đối tác GHTK không hợp lệ',
  [INTEGRATION_ERROR_CODES.GHTK_API_ERROR]: 'Lỗi API GHTK',
  
  // Ahamove
  [INTEGRATION_ERROR_CODES.AHAMOVE_INVALID_API_KEY]: 'API Key Ahamove không hợp lệ',
  [INTEGRATION_ERROR_CODES.AHAMOVE_INVALID_MOBILE]: 'Số điện thoại Ahamove không hợp lệ',
  [INTEGRATION_ERROR_CODES.AHAMOVE_AUTH_FAILED]: 'Xác thực Ahamove thất bại',
  [INTEGRATION_ERROR_CODES.AHAMOVE_API_ERROR]: 'Lỗi API Ahamove',
  
  // J&T
  [INTEGRATION_ERROR_CODES.JT_INVALID_USERNAME]: 'Username J&T không hợp lệ',
  [INTEGRATION_ERROR_CODES.JT_INVALID_API_KEY]: 'API Key J&T không hợp lệ',
  [INTEGRATION_ERROR_CODES.JT_SIGNATURE_ERROR]: 'Lỗi chữ ký J&T',
  [INTEGRATION_ERROR_CODES.JT_API_ERROR]: 'Lỗi API J&T',
  
  // Database
  [INTEGRATION_ERROR_CODES.USER_PROVIDER_NOT_FOUND]: 'Không tìm thấy cấu hình nhà cung cấp',
  [INTEGRATION_ERROR_CODES.USER_PROVIDER_CREATE_FAILED]: 'Tạo cấu hình nhà cung cấp thất bại',
  [INTEGRATION_ERROR_CODES.USER_PROVIDER_UPDATE_FAILED]: 'Cập nhật cấu hình nhà cung cấp thất bại',
  [INTEGRATION_ERROR_CODES.USER_PROVIDER_DELETE_FAILED]: 'Xóa cấu hình nhà cung cấp thất bại',
} as const;

/**
 * HTTP status codes cho từng error
 */
export const INTEGRATION_ERROR_HTTP_STATUS = {
  [INTEGRATION_ERROR_CODES.INVALID_DATA]: 400,
  [INTEGRATION_ERROR_CODES.ENCRYPTION_FAILED]: 500,
  [INTEGRATION_ERROR_CODES.DECRYPTION_FAILED]: 500,
  
  [INTEGRATION_ERROR_CODES.PROVIDER_NOT_FOUND]: 404,
  [INTEGRATION_ERROR_CODES.UNSUPPORTED_PROVIDER]: 400,
  [INTEGRATION_ERROR_CODES.PROVIDER_CONFIG_INVALID]: 400,
  
  [INTEGRATION_ERROR_CODES.CONNECTION_TEST_FAILED]: 400,
  [INTEGRATION_ERROR_CODES.INVALID_CREDENTIALS]: 401,
  [INTEGRATION_ERROR_CODES.PROVIDER_UNAVAILABLE]: 503,
  [INTEGRATION_ERROR_CODES.TIMEOUT_ERROR]: 408,
  [INTEGRATION_ERROR_CODES.NETWORK_ERROR]: 503,
  
  // GHN
  [INTEGRATION_ERROR_CODES.GHN_INVALID_TOKEN]: 401,
  [INTEGRATION_ERROR_CODES.GHN_SHOP_NOT_FOUND]: 404,
  [INTEGRATION_ERROR_CODES.GHN_API_ERROR]: 502,
  
  // GHTK
  [INTEGRATION_ERROR_CODES.GHTK_INVALID_TOKEN]: 401,
  [INTEGRATION_ERROR_CODES.GHTK_INVALID_PARTNER_CODE]: 401,
  [INTEGRATION_ERROR_CODES.GHTK_API_ERROR]: 502,
  
  // Ahamove
  [INTEGRATION_ERROR_CODES.AHAMOVE_INVALID_API_KEY]: 401,
  [INTEGRATION_ERROR_CODES.AHAMOVE_INVALID_MOBILE]: 400,
  [INTEGRATION_ERROR_CODES.AHAMOVE_AUTH_FAILED]: 401,
  [INTEGRATION_ERROR_CODES.AHAMOVE_API_ERROR]: 502,
  
  // J&T
  [INTEGRATION_ERROR_CODES.JT_INVALID_USERNAME]: 401,
  [INTEGRATION_ERROR_CODES.JT_INVALID_API_KEY]: 401,
  [INTEGRATION_ERROR_CODES.JT_SIGNATURE_ERROR]: 400,
  [INTEGRATION_ERROR_CODES.JT_API_ERROR]: 502,
  
  // Database
  [INTEGRATION_ERROR_CODES.USER_PROVIDER_NOT_FOUND]: 404,
  [INTEGRATION_ERROR_CODES.USER_PROVIDER_CREATE_FAILED]: 500,
  [INTEGRATION_ERROR_CODES.USER_PROVIDER_UPDATE_FAILED]: 500,
  [INTEGRATION_ERROR_CODES.USER_PROVIDER_DELETE_FAILED]: 500,
} as const;
