import { NestFactory } from '@nestjs/core';
import { AppModule } from '@/app.module';
import { EmployeeRepository } from '../repositories/employee.repository';
import { DataSource } from 'typeorm';

/**
 * Script debug để kiểm tra dữ liệu thống kê nhân viên
 */
async function debugEmployeeStatistics() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const dataSource = app.get(DataSource);
  const employeeRepository = app.get(EmployeeRepository);

  try {
    console.log('=== DEBUG EMPLOYEE STATISTICS ===\n');

    // Test với tenantId = 1 (hoặc thay đổi theo dữ liệu thực tế)
    const tenantId = 1;

    // 1. Kiểm tra tổng số employees
    console.log('1. TỔNG SỐ EMPLOYEES:');
    const totalQuery = `SELECT COUNT(*) as total FROM employees WHERE tenant_id = $1`;
    const totalResult = await dataSource.query(totalQuery, [tenantId]);
    console.log(`   Total employees: ${totalResult[0]?.total || 0}\n`);

    // 2. Kiểm tra employment_type distribution
    console.log('2. EMPLOYMENT TYPE DISTRIBUTION:');
    const employmentQuery = `
      SELECT 
        employment_type,
        COUNT(*) as count
      FROM employees 
      WHERE tenant_id = $1 
      GROUP BY employment_type
      ORDER BY count DESC
    `;
    const employmentResult = await dataSource.query(employmentQuery, [tenantId]);
    console.log('   Raw employment data:', employmentResult);

    // 3. Kiểm tra department distribution
    console.log('\n3. DEPARTMENT DISTRIBUTION:');
    const departmentQuery = `
      SELECT 
        e.department_id,
        d.name as department_name,
        COUNT(e.id) as count
      FROM employees e
      LEFT JOIN departments d ON d.id = e.department_id AND d.tenant_id = $1
      WHERE e.tenant_id = $1 AND e.department_id IS NOT NULL
      GROUP BY e.department_id, d.name
      ORDER BY count DESC
    `;
    const departmentResult = await dataSource.query(departmentQuery, [tenantId]);
    console.log('   Raw department data:', departmentResult);

    // 4. Kiểm tra employees không có department
    console.log('\n4. EMPLOYEES WITHOUT DEPARTMENT:');
    const noDeptQuery = `SELECT COUNT(*) as count FROM employees WHERE tenant_id = $1 AND department_id IS NULL`;
    const noDeptResult = await dataSource.query(noDeptQuery, [tenantId]);
    console.log(`   Employees without department: ${noDeptResult[0]?.count || 0}\n`);

    // 5. Test repository methods
    console.log('5. REPOSITORY METHOD RESULTS:');
    
    console.log('\n   Department Distribution:');
    const deptStats = await employeeRepository.getDepartmentDistribution(tenantId);
    console.log('   Repository result:', JSON.stringify(deptStats, null, 2));

    console.log('\n   Contract Distribution:');
    const contractStats = await employeeRepository.getContractDistribution(tenantId);
    console.log('   Repository result:', JSON.stringify(contractStats, null, 2));

    // 6. Kiểm tra departments table
    console.log('\n6. DEPARTMENTS TABLE:');
    const deptsQuery = `SELECT id, name, tenant_id FROM departments WHERE tenant_id = $1`;
    const deptsResult = await dataSource.query(deptsQuery, [tenantId]);
    console.log('   Departments:', deptsResult);

  } catch (error) {
    console.error('Error during debug:', error);
  } finally {
    await app.close();
  }
}

// Chạy script
debugEmployeeStatistics().catch(console.error);
