import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho việc tr<PERSON> về presigned URL sau khi tạo website
 */
export class CreateWebsiteResponseDto {
  @ApiProperty({
    description: 'Presigned URL để upload logo (chỉ có khi logoMime được cung cấp)',
    example: 'https://s3.amazonaws.com/bucket/path/to/upload?signature=...',
    nullable: true,
  })
  @Expose()
  logoUploadUrl?: string;
}
