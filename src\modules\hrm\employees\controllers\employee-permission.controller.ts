import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@/modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { SWAGGER_API_TAG } from '@/common/swagger/swagger.tags';
import { RequirePermissionEnum } from '@/modules/auth/decorators/require-module-action.decorator';
import { Permission } from '@/modules/auth/enum/permission.enum';
import { EmployeePermissionService } from '../services/employee-permission.service';
import {
  PermissionGroupsResponseDto,
  UserPermissionsResponseDto,
} from '../dto/permission/permission-group.dto';
import {
  UpdateEmployeeRoleDto,
  UpdateEmployeeRoleResponseDto,
} from '../dto/permission/update-employee-role.dto';
import {
  UpdateEmployeePermissionDto,
  UpdateEmployeePermissionResponseDto,
} from '../dto/permission/update-employee-permission.dto';
import {
  UpdateRolePermissionDto,
  UpdateRolePermissionResponseDto,
} from '../dto/permission/update-role-permission.dto';

/**
 * Controller quản lý phân quyền cho nhân viên
 */
@ApiTags(SWAGGER_API_TAG.EMPLOYEE)
@ApiExtraModels(
  ApiResponseDto,
  PermissionGroupsResponseDto,
  UserPermissionsResponseDto,
  UpdateEmployeeRoleResponseDto,
  UpdateEmployeePermissionResponseDto,
  UpdateRolePermissionResponseDto,
)
@Controller('api/hrm/employees/permissions')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class EmployeePermissionController {
  constructor(
    private readonly employeePermissionService: EmployeePermissionService,
  ) {}

  /**
   * Lấy danh sách các quyền theo từng nhóm module
   */
  @Get('groups')
  @RequirePermissionEnum(Permission.PERMISSION_VIEW_LIST)
  @ApiOperation({ summary: 'Lấy danh sách các quyền theo từng nhóm module' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách các quyền theo từng nhóm module',
    schema: ApiResponseDto.getSchema(PermissionGroupsResponseDto),
  })
  async getPermissionGroups(): Promise<
    ApiResponseDto<PermissionGroupsResponseDto>
  > {
    const result = await this.employeePermissionService.getPermissionGroups();
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy danh sách quyền của tài khoản user
   */
  @Get('user/:userId')
  @RequirePermissionEnum(Permission.USER_VIEW_DETAIL)
  @ApiOperation({ summary: 'Lấy danh sách quyền của tài khoản user' })
  @ApiParam({
    name: 'userId',
    description: 'ID của người dùng',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách quyền của tài khoản user',
    schema: ApiResponseDto.getSchema(UserPermissionsResponseDto),
  })
  async getUserPermissions(
    @Param('userId', ParseIntPipe) userId: number,
  ): Promise<ApiResponseDto<UserPermissionsResponseDto>> {
    const result =
      await this.employeePermissionService.getUserPermissions(userId);
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật vai trò cho nhân viên
   */
  @Post('update-employee-role')
  @RequirePermissionEnum(Permission.USER_ASSIGN_ROLE)
  @ApiOperation({ summary: 'Cập nhật vai trò cho nhân viên' })
  @ApiResponse({
    status: 200,
    description: 'Vai trò của nhân viên đã được cập nhật',
    schema: ApiResponseDto.getSchema(UpdateEmployeeRoleResponseDto),
  })
  async updateEmployeeRole(
    @Body() dto: UpdateEmployeeRoleDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UpdateEmployeeRoleResponseDto>> {
    const result = await this.employeePermissionService.updateEmployeeRole(
      Number(user.tenantId),
      dto,
      user.id,
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật quyền cho nhân viên
   */
  @Post('update-employee-permission')
  @RequirePermissionEnum(Permission.USER_ASSIGN_ROLE)
  @ApiOperation({ summary: 'Cập nhật quyền trực tiếp cho nhân viên' })
  @ApiResponse({
    status: 200,
    description: 'Quyền của nhân viên đã được cập nhật',
    schema: ApiResponseDto.getSchema(UpdateEmployeePermissionResponseDto),
  })
  async updateEmployeePermission(
    @Body() dto: UpdateEmployeePermissionDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UpdateEmployeePermissionResponseDto>> {
    const result =
      await this.employeePermissionService.updateEmployeePermission(
        Number(user.tenantId),
        dto,
        user.id,
      );
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật quyền cho vai trò
   */
  @Post('update-role-permission')
  @RequirePermissionEnum(Permission.ROLE_ASSIGN_PERMISSION)
  @ApiOperation({ summary: 'Cập nhật quyền cho vai trò' })
  @ApiResponse({
    status: 200,
    description: 'Quyền của vai trò đã được cập nhật',
    schema: ApiResponseDto.getSchema(UpdateRolePermissionResponseDto),
  })
  async updateRolePermission(
    @Body() dto: UpdateRolePermissionDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UpdateRolePermissionResponseDto>> {
    const result = await this.employeePermissionService.updateRolePermission(
      dto,
      user.id,
    );
    return ApiResponseDto.success(result);
  }
}
