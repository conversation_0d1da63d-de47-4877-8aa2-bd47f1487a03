import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin nhà cung cấp AI
 */
export class AiProviderResponseDto {
  @ApiProperty({
    description: 'ID của nhà cung cấp AI',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Key nội bộ định danh nhà cung cấp',
    example: 'openai'
  })
  providerKey: string;

  @ApiProperty({
    description: 'Tên hiển thị của nhà cung cấp',
    example: 'OpenAI'
  })
  name: string;

  @ApiProperty({
    description: 'Đường dẫn icon của nhà cung cấp',
    example: 'https://example.com/openai-icon.png',
    nullable: true
  })
  icon?: string;

  @ApiProperty({
    description: 'URL mặc định để gọi API',
    example: 'https://api.openai.com/v1',
    nullable: true
  })
  baseUrl?: string;
}
