import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, <PERSON>Length, MinLength } from 'class-validator';

/**
 * DTO cho việc tra cứu thông tin tài khoản ngân hàng
 */
export class AccountLookupDto {
  @ApiProperty({
    description: 'ID ngân hàng',
    example: '1'
  })
  @IsNotEmpty()
  @IsString()
  bankId: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********'
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(5)
  @MaxLength(30)
  accountNumber: string;
}
