import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc cập nhật logo S3 key của website sau <PERSON>hi upload thành công
 */
export class UpdateWebsiteLogoDto {
  @ApiProperty({
    description: 'S3 key của logo đã upload thành công',
    example: 'integration/IMAGE/2024/01/user_38/1234567890-uuid.png',
  })
  @IsNotEmpty({ message: 'Logo S3 key không được để trống' })
  @IsString({ message: 'Logo S3 key phải là chuỗi' })
  logoS3Key: string;
}
