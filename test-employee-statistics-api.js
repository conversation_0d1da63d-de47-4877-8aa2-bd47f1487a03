const axios = require('axios');

// Cấu hình <PERSON>
const API_BASE_URL = 'http://localhost:3001';
const API_VERSION = '/v1';

// Hàm helper để gọi API
async function makeRequest(endpoint, method = 'GET', data = null, token = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${API_VERSION}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    if (data && (method === 'POST' || method === 'PATCH' || method === 'PUT')) {
      config.data = data;
    }

    const response = await axios(config);
    return response;
  } catch (error) {
    return error.response || { status: 500, data: { message: error.message } };
  }
}

// Hàm đăng nhập để lấy token
async function login() {
  console.log('🔐 Đăng nhập để lấy token...');
  const loginData = {
    email: '<EMAIL>',
    password: 'admin123',
  };

  const response = await makeRequest('/api/auth/users/login', 'POST', loginData);
  
  if (response.status === 200) {
    console.log('✅ Đăng nhập thành công!');
    return response.data.data.accessToken;
  } else {
    console.log('❌ Đăng nhập thất bại:', response.data);
    return null;
  }
}

// Hàm test chính
async function testEmployeeStatisticsAPIs() {
  console.log('🚀 Bắt đầu test Employee Statistics APIs\n');

  // Đăng nhập
  const authToken = await login();
  if (!authToken) {
    console.log('❌ Không thể lấy token, dừng test');
    return;
  }
  console.log('');

  // Test 1: API phân bố theo phòng ban
  console.log('📊 Test 1: GET Department Distribution');
  try {
    const response = await makeRequest('/api/hrm/employees/statistics/department-distribution', 'GET', null, authToken);
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      const data = response.data.data;
      console.log('✅ Department Distribution:');
      console.log(`   📋 Tổng số nhân viên: ${data.totalEmployees}`);
      console.log(`   🏢 Số phòng ban có nhân viên: ${data.departments.length}`);
      console.log(`   👥 Nhân viên chưa phân phòng ban: ${data.unassignedEmployees} (${data.unassignedPercentage}%)`);
      
      console.log('\n   📈 Chi tiết theo phòng ban:');
      data.departments.forEach((dept, index) => {
        console.log(`   ${index + 1}. ${dept.departmentName}: ${dept.employeeCount} nhân viên (${dept.percentage}%)`);
      });
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 2: API phân bố theo loại hợp đồng
  console.log('📊 Test 2: GET Contract Distribution');
  try {
    const response = await makeRequest('/api/hrm/employees/statistics/contract-distribution', 'GET', null, authToken);
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      const data = response.data.data;
      console.log('✅ Contract Distribution:');
      console.log(`   📋 Tổng số nhân viên: ${data.totalEmployees}`);
      console.log(`   📄 Số loại hợp đồng: ${data.contracts.length}`);
      console.log(`   ❓ Nhân viên chưa có hợp đồng: ${data.noContractEmployees} (${data.noContractPercentage}%)`);
      
      console.log('\n   📈 Chi tiết theo loại hợp đồng:');
      data.contracts.forEach((contract, index) => {
        console.log(`   ${index + 1}. ${contract.contractTypeName}: ${contract.employeeCount} nhân viên (${contract.percentage}%)`);
      });
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  // Test 3: API thống kê thâm niên
  console.log('📊 Test 3: GET Tenure Statistics');
  try {
    const response = await makeRequest('/api/hrm/employees/statistics/tenure-stats', 'GET', null, authToken);
    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      const data = response.data.data;
      console.log('✅ Tenure Statistics:');
      console.log(`   📋 Tổng số nhân viên: ${data.totalEmployees}`);
      console.log(`   ⏱️ Thâm niên trung bình: ${data.averageTenureYears} năm`);
      console.log(`   ⚠️ Sắp hết thử việc: ${data.probationEndingSoon} nhân viên`);
      console.log(`   📈 Tỷ lệ tăng trưởng tháng: ${data.monthlyGrowthRate}%`);
      console.log(`   🆕 Nhân viên mới tháng này: ${data.newEmployeesThisMonth}`);
      console.log(`   📅 Nhân viên mới tháng trước: ${data.newEmployeesLastMonth}`);
      
      console.log('\n   📊 Phân bố theo thâm niên:');
      console.log(`   • Dưới 1 năm: ${data.tenureBreakdown.lessThan1Year} nhân viên`);
      console.log(`   • 1-3 năm: ${data.tenureBreakdown.oneToThreeYears} nhân viên`);
      console.log(`   • 3-5 năm: ${data.tenureBreakdown.threeToFiveYears} nhân viên`);
      console.log(`   • Trên 5 năm: ${data.tenureBreakdown.moreThanFiveYears} nhân viên`);
    } else {
      console.log('❌ Failed:', response.data);
    }
  } catch (error) {
    console.log('❌ Error:', error.message);
  }
  console.log('');

  console.log('🎉 Hoàn thành test Employee Statistics APIs!');
}

// Chạy test
testEmployeeStatisticsAPIs().catch(console.error);
