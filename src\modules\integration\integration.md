# Phân tích và Đề xuất Giao diện Frontend cho Module Integration

## 1. Tổng quan

Module Integration quản lý việc tích hợp hệ thống với các dịch vụ và nền tảng bên ngoài. Dựa trên phân tích cấu trúc thư mục backend, module này bao gồm các chức năng tích hợp với:

- Nhà cung cấp AI (AI Providers)
- Cấu hình email server
- Cấu hình SMS server
- T<PERSON>ch hợp Facebook (trang và cá nhân)
- Cổng thanh toán (Payment Gateways)
- Quản lý website của người dùng
- Quản lý API keys

## 2. Cấu trúc dữ liệu chính

Dựa trên các entities đã xem, module Integration bao gồm các dữ liệu chính:

### AI Provider
- id: ID nhà cung cấp AI
- name: <PERSON><PERSON><PERSON> nhà cung cấp
- description: Mô tả
- baseUrl: URL cơ sở
- apiVersion: Phiên bản API
- isActive: Tr<PERSON>ng thái hoạt động
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

### AI Provider Model
- id: ID model
- providerId: ID nhà cung cấp AI
- name: Tên model
- description: Mô tả
- modelId: ID model từ nhà cung cấp
- maxTokens: Số token tối đa
- temperature: Nhiệt độ
- isActive: Trạng thái hoạt động
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

### AI Provider Config
- id: ID cấu hình
- providerId: ID nhà cung cấp AI
- apiKey: Khóa API
- organizationId: ID tổ chức
- isActive: Trạng thái hoạt động
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

### Email Server Configuration
- id: ID cấu hình
- host: Máy chủ email
- port: Cổng
- username: Tên đăng nhập
- password: Mật khẩu
- fromEmail: Email gửi
- fromName: Tên người gửi
- isActive: Trạng thái hoạt động
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

### SMS Server Configuration
- id: ID cấu hình
- provider: Nhà cung cấp SMS
- apiKey: Khóa API
- apiSecret: Bí mật API
- fromPhone: Số điện thoại gửi
- isActive: Trạng thái hoạt động
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

### Payment Gateway
- id: ID cổng thanh toán
- name: Tên cổng thanh toán
- code: Mã cổng thanh toán
- description: Mô tả
- merchantId: ID đối tác
- apiKey: Khóa API
- apiSecret: Bí mật API
- isActive: Trạng thái hoạt động
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

### User Website
- id: ID website
- userId: ID người dùng
- url: URL website
- name: Tên website
- description: Mô tả
- isActive: Trạng thái hoạt động
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

### User Key
- id: ID khóa
- userId: ID người dùng
- apiKey: Khóa API
- description: Mô tả
- expiredAt: Thời gian hết hạn
- isActive: Trạng thái hoạt động
- createdAt: Thời gian tạo
- updatedAt: Thời gian cập nhật

## 3. Đề xuất Giao diện Frontend

### 3.1. Giao diện Admin

#### 3.1.1. Trang Quản lý Nhà cung cấp AI
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm nhà cung cấp theo tên
  - Lọc theo trạng thái (active/inactive)
  - Phân trang
  - Thêm nhà cung cấp mới
  - Sửa thông tin nhà cung cấp
  - Vô hiệu hóa/kích hoạt nhà cung cấp
- **Cột hiển thị**:
  - ID
  - Tên nhà cung cấp
  - Mô tả
  - URL cơ sở
  - Phiên bản API
  - Trạng thái
  - Ngày tạo
  - Hành động (Sửa, Xóa, Quản lý Models)

#### 3.1.2. Form Thêm/Sửa Nhà cung cấp AI
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Tên nhà cung cấp (input text)
  - Mô tả (textarea)
  - URL cơ sở (input text)
  - Phiên bản API (input text)
  - Trạng thái (toggle/switch)

#### 3.1.3. Trang Quản lý Models AI
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm model theo tên
  - Lọc theo nhà cung cấp
  - Lọc theo trạng thái (active/inactive)
  - Phân trang
  - Thêm model mới
  - Sửa thông tin model
  - Vô hiệu hóa/kích hoạt model
- **Cột hiển thị**:
  - ID
  - Tên model
  - Nhà cung cấp
  - Mô tả
  - ID model từ nhà cung cấp
  - Số token tối đa
  - Nhiệt độ
  - Trạng thái
  - Hành động (Sửa, Xóa)

#### 3.1.4. Form Thêm/Sửa Model AI
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Nhà cung cấp (select)
  - Tên model (input text)
  - Mô tả (textarea)
  - ID model từ nhà cung cấp (input text)
  - Số token tối đa (input number)
  - Nhiệt độ (input number, slider)
  - Trạng thái (toggle/switch)

#### 3.1.5. Trang Quản lý Cấu hình AI
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Lọc theo nhà cung cấp
  - Thêm cấu hình mới
  - Sửa thông tin cấu hình
  - Vô hiệu hóa/kích hoạt cấu hình
- **Cột hiển thị**:
  - ID
  - Nhà cung cấp
  - API Key (hiển thị che giấu)
  - ID tổ chức
  - Trạng thái
  - Ngày tạo
  - Hành động (Sửa, Xóa)

#### 3.1.6. Form Thêm/Sửa Cấu hình AI
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Nhà cung cấp (select)
  - API Key (input password)
  - ID tổ chức (input text)
  - Trạng thái (toggle/switch)

#### 3.1.7. Trang Quản lý Cấu hình Email Server
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Thêm cấu hình mới
  - Sửa thông tin cấu hình
  - Vô hiệu hóa/kích hoạt cấu hình
  - Kiểm tra kết nối
- **Cột hiển thị**:
  - ID
  - Máy chủ
  - Cổng
  - Tên đăng nhập
  - Email gửi
  - Tên người gửi
  - Trạng thái
  - Hành động (Sửa, Xóa, Kiểm tra)

#### 3.1.8. Form Thêm/Sửa Cấu hình Email Server
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Máy chủ (input text)
  - Cổng (input number)
  - Tên đăng nhập (input text)
  - Mật khẩu (input password)
  - Email gửi (input email)
  - Tên người gửi (input text)
  - Trạng thái (toggle/switch)
  - Nút "Kiểm tra kết nối"

#### 3.1.9. Trang Quản lý Cấu hình SMS Server
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Thêm cấu hình mới
  - Sửa thông tin cấu hình
  - Vô hiệu hóa/kích hoạt cấu hình
  - Kiểm tra kết nối
- **Cột hiển thị**:
  - ID
  - Nhà cung cấp
  - API Key (hiển thị che giấu)
  - Số điện thoại gửi
  - Trạng thái
  - Hành động (Sửa, Xóa, Kiểm tra)

#### 3.1.10. Form Thêm/Sửa Cấu hình SMS Server
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Nhà cung cấp (select)
  - API Key (input password)
  - API Secret (input password)
  - Số điện thoại gửi (input text)
  - Trạng thái (toggle/switch)
  - Nút "Kiểm tra kết nối"

#### 3.1.11. Trang Quản lý Cổng Thanh toán
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm cổng thanh toán theo tên, mã
  - Lọc theo trạng thái (active/inactive)
  - Phân trang
  - Thêm cổng thanh toán mới
  - Sửa thông tin cổng thanh toán
  - Vô hiệu hóa/kích hoạt cổng thanh toán
- **Cột hiển thị**:
  - ID
  - Tên cổng thanh toán
  - Mã cổng thanh toán
  - Mô tả
  - ID đối tác
  - Trạng thái
  - Hành động (Sửa, Xóa)

#### 3.1.12. Form Thêm/Sửa Cổng Thanh toán
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Tên cổng thanh toán (input text)
  - Mã cổng thanh toán (input text)
  - Mô tả (textarea)
  - ID đối tác (input text)
  - API Key (input password)
  - API Secret (input password)
  - Trạng thái (toggle/switch)

### 3.2. Giao diện Người dùng

#### 3.2.1. Trang Quản lý Website
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm website theo tên, URL
  - Phân trang
  - Thêm website mới
  - Sửa thông tin website
  - Xóa website
- **Cột hiển thị**:
  - ID
  - Tên website
  - URL
  - Mô tả
  - Trạng thái
  - Ngày tạo
  - Hành động (Sửa, Xóa, Cài đặt Script)

#### 3.2.2. Form Thêm/Sửa Website
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Tên website (input text)
  - URL (input text)
  - Mô tả (textarea)
  - Trạng thái (toggle/switch)

#### 3.2.3. Trang Cài đặt Script
- **Thành phần**: Hướng dẫn và code snippet
- **Chức năng**:
  - Hiển thị hướng dẫn cài đặt
  - Cung cấp code snippet để nhúng vào website
  - Nút sao chép code

#### 3.2.4. Trang Quản lý API Keys
- **Thành phần**: Bảng dữ liệu (DataTable)
- **Chức năng**:
  - Tìm kiếm API key theo mô tả
  - Phân trang
  - Tạo API key mới
  - Xóa API key
  - Vô hiệu hóa/kích hoạt API key
- **Cột hiển thị**:
  - ID
  - API Key (hiển thị che giấu)
  - Mô tả
  - Ngày hết hạn
  - Trạng thái
  - Ngày tạo
  - Hành động (Xóa, Vô hiệu hóa)

#### 3.2.5. Form Tạo API Key
- **Thành phần**: Form với các trường
- **Trường dữ liệu**:
  - Mô tả (input text)
  - Thời gian hết hạn (datepicker)

#### 3.2.6. Trang Tích hợp Facebook
- **Thành phần**: Danh sách tài khoản và trang đã kết nối
- **Chức năng**:
  - Kết nối tài khoản Facebook cá nhân
  - Kết nối trang Facebook
  - Quản lý quyền
  - Ngắt kết nối
- **Hiển thị**:
  - Danh sách tài khoản cá nhân đã kết nối
  - Danh sách trang đã kết nối
  - Trạng thái kết nối
  - Quyền đã cấp
  - Hành động (Ngắt kết nối, Cập nhật quyền)

## 4. Đề xuất Cấu trúc Component

### 4.1. Components chung
- **Layout**: AdminLayout và UserLayout
- **AuthGuard**: Bảo vệ các route yêu cầu đăng nhập
- **DataTable**: Component bảng dữ liệu với phân trang, sắp xếp, tìm kiếm
- **Modal**: Component modal cho các form thêm/sửa
- **Toast/Notification**: Hiển thị thông báo thành công/lỗi
- **CopyToClipboard**: Component sao chép vào clipboard

### 4.2. Components cho Admin
- **AIProviderList**: Danh sách nhà cung cấp AI
- **AIProviderForm**: Form thêm/sửa nhà cung cấp AI
- **AIModelList**: Danh sách model AI
- **AIModelForm**: Form thêm/sửa model AI
- **AIConfigList**: Danh sách cấu hình AI
- **AIConfigForm**: Form thêm/sửa cấu hình AI
- **EmailServerList**: Danh sách cấu hình email server
- **EmailServerForm**: Form thêm/sửa cấu hình email server
- **SMSServerList**: Danh sách cấu hình SMS server
- **SMSServerForm**: Form thêm/sửa cấu hình SMS server
- **PaymentGatewayList**: Danh sách cổng thanh toán
- **PaymentGatewayForm**: Form thêm/sửa cổng thanh toán

### 4.3. Components cho Người dùng
- **WebsiteList**: Danh sách website
- **WebsiteForm**: Form thêm/sửa website
- **ScriptInstallation**: Hướng dẫn cài đặt script
- **APIKeyList**: Danh sách API key
- **APIKeyForm**: Form tạo API key
- **FacebookIntegration**: Tích hợp Facebook

### 4.4. Đề xuất Routing

#### Admin Routes
```
/admin/integration
  /ai-providers            # Danh sách nhà cung cấp AI
    /create                # Thêm nhà cung cấp mới
    /:id/edit              # Sửa thông tin nhà cung cấp
  /ai-models               # Danh sách model AI
    /create                # Thêm model mới
    /:id/edit              # Sửa thông tin model
  /ai-configs              # Danh sách cấu hình AI
    /create                # Thêm cấu hình mới
    /:id/edit              # Sửa thông tin cấu hình
  /email-servers           # Danh sách cấu hình email server
    /create                # Thêm cấu hình mới
    /:id/edit              # Sửa thông tin cấu hình
  /sms-servers             # Danh sách cấu hình SMS server
    /create                # Thêm cấu hình mới
    /:id/edit              # Sửa thông tin cấu hình
  /payment-gateways        # Danh sách cổng thanh toán
    /create                # Thêm cổng thanh toán mới
    /:id/edit              # Sửa thông tin cổng thanh toán
```

#### User Routes
```
/integration
  /websites               # Danh sách website
    /create               # Thêm website mới
    /:id/edit             # Sửa thông tin website
    /:id/script           # Cài đặt script
  /api-keys               # Danh sách API key
    /create               # Tạo API key mới
  /facebook               # Tích hợp Facebook
```

## 5. Đề xuất State Management

### 5.1. Redux/Context API Store
- **aiProviders**: Danh sách nhà cung cấp AI, loading state, error state
- **aiModels**: Danh sách model AI, loading state, error state
- **aiConfigs**: Danh sách cấu hình AI, loading state, error state
- **emailServers**: Danh sách cấu hình email server, loading state, error state
- **smsServers**: Danh sách cấu hình SMS server, loading state, error state
- **paymentGateways**: Danh sách cổng thanh toán, loading state, error state
- **websites**: Danh sách website, loading state, error state
- **apiKeys**: Danh sách API key, loading state, error state
- **facebookIntegration**: Thông tin tích hợp Facebook, loading state, error state
- **ui**: Trạng thái UI (filters, search, pagination)

### 5.2. Actions/Reducers
- **aiProviders**: fetchProviders, createProvider, updateProvider, deleteProvider
- **aiModels**: fetchModels, createModel, updateModel, deleteModel
- **aiConfigs**: fetchConfigs, createConfig, updateConfig, deleteConfig
- **emailServers**: fetchEmailServers, createEmailServer, updateEmailServer, deleteEmailServer, testEmailServer
- **smsServers**: fetchSMSServers, createSMSServer, updateSMSServer, deleteSMSServer, testSMSServer
- **paymentGateways**: fetchPaymentGateways, createPaymentGateway, updatePaymentGateway, deletePaymentGateway
- **websites**: fetchWebsites, createWebsite, updateWebsite, deleteWebsite
- **apiKeys**: fetchAPIKeys, createAPIKey, deleteAPIKey, toggleAPIKey
- **facebookIntegration**: connectFacebook, disconnectFacebook, updatePermissions

## 6. Đề xuất Công nghệ

- **Framework**: React
- **UI Library**: Material-UI hoặc Ant Design
- **State Management**: Redux Toolkit hoặc Context API
- **Form Handling**: Formik hoặc React Hook Form
- **Validation**: Yup
- **HTTP Client**: Axios
- **Routing**: React Router
- **Internationalization**: i18next
- **Code Highlighting**: Prism.js hoặc Highlight.js (cho hiển thị code snippet)
- **Date Handling**: date-fns hoặc moment.js

## 7. Mockups

Dưới đây là đề xuất mockup cho một số màn hình chính:

### 7.1. Trang Quản lý Nhà cung cấp AI (Admin)
```
+----------------------------------+
| [Sidebar] | Header               |
|           | [Thêm mới] [Tìm kiếm]|
|           |                      |
|           | Lọc: [Trạng thái]    |
|           |                      |
|           | +------------------+ |
|           | | Danh sách nhà    | |
|           | | cung cấp AI      | |
|           | | (DataTable)      | |
|           | |                  | |
|           | |                  | |
|           | |                  | |
|           | +------------------+ |
|           | [Pagination]         |
+----------------------------------+
```

### 7.2. Form Thêm/Sửa Nhà cung cấp AI (Admin)
```
+----------------------------------+
| [Sidebar] | Header               |
|           |                      |
|           | [Thêm nhà cung cấp AI] |
|           |                      |
|           | +------------------+ |
|           | | Tên nhà cung cấp | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Mô tả            | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | URL cơ sở        | |
|           | +------------------+ |
|           |                      |
|           | +------------------+ |
|           | | Phiên bản API    | |
|           | +------------------+ |
|           |                      |
|           | | Trạng thái [On/Off]|
|           | +------------------+ |
|           |                      |
|           | [Lưu] [Hủy]         |
+----------------------------------+
```

### 7.3. Trang Quản lý Website (Người dùng)
```
+----------------------------------+
| [Header]                         |
|                                  |
| [Quản lý Website]                |
| [Thêm mới] [Tìm kiếm]           |
|                                  |
| +----------------------------+   |
| | Danh sách website         |   |
| | (DataTable)               |   |
| |                           |   |
| |                           |   |
| |                           |   |
| +----------------------------+   |
| [Pagination]                     |
|                                  |
+----------------------------------+
```

### 7.4. Trang Cài đặt Script (Người dùng)
```
+----------------------------------+
| [Header]                         |
|                                  |
| [Cài đặt Script cho Website XYZ] |
|                                  |
| [Hướng dẫn cài đặt]              |
| 1. Sao chép đoạn mã dưới đây    |
| 2. Dán vào phần <head> của      |
|    website của bạn              |
|                                  |
| +----------------------------+   |
| | <script>                  |   |
| |   // Code snippet         |   |
| |   ...                     |   |
| | </script>                 |   |
| +----------------------------+   |
| [Sao chép]                       |
|                                  |
| [Kiểm tra cài đặt]               |
|                                  |
+----------------------------------+
```

### 7.5. Trang Quản lý API Keys (Người dùng)
```
+----------------------------------+
| [Header]                         |
|                                  |
| [Quản lý API Keys]               |
| [Tạo mới] [Tìm kiếm]            |
|                                  |
| +----------------------------+   |
| | Danh sách API keys        |   |
| | (DataTable)               |   |
| |                           |   |
| |                           |   |
| |                           |   |
| +----------------------------+   |
| [Pagination]                     |
|                                  |
+----------------------------------+
```

## 8. Kết luận

Dựa trên phân tích cấu trúc thư mục backend và các entities, module Integration cần một giao diện frontend đầy đủ để quản lý việc tích hợp hệ thống với các dịch vụ và nền tảng bên ngoài. Giao diện đề xuất tập trung vào việc cung cấp công cụ quản lý hiệu quả cho admin và trải nghiệm người dùng tốt cho người dùng cuối.

Việc triển khai nên được thực hiện theo từng giai đoạn, bắt đầu từ các chức năng cơ bản như quản lý nhà cung cấp AI và cấu hình, sau đó mở rộng đến các tích hợp khác như email, SMS, cổng thanh toán và Facebook.
