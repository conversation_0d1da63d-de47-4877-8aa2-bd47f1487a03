import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { SendEmailDto } from '../interface/send-email.dto';
import { AppException, ErrorCode } from '@/common';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly transporter: nodemailer.Transporter;
  private readonly emailConfig: any;

  constructor(private readonly configService: ConfigService) {
    // L<PERSON>y cấu hình email từ ConfigService với tên biến đúng
    this.emailConfig = {
      smtpHost: this.configService.get<string>('MAIL_HOST'),
      smtpPort: this.configService.get<number>('MAIL_PORT'),
      smtpUser: this.configService.get<string>('MAIL_USERNAME'),
      smtpPass: this.configService.get<string>('MAIL_PASSWORD'),
      smtpSecure: this.configService.get<string>('MAIL_SECURE') === 'true',
      defaultFrom: this.configService.get<string>('MAIL_DEFAULT_FROM'),
    };

    // Kiểm tra xem có cấu hình SMTP không
    if (this.emailConfig.smtpUser && this.emailConfig.smtpPass) {
      // Tạo transporter với cấu hình SMTP
      this.transporter = this.createTransporter();
      this.logger.log('Email service initialized with SMTP configuration');
      this.logger.log(`SMTP Host: ${this.emailConfig.smtpHost}:${this.emailConfig.smtpPort}`);
      this.logger.log(`SMTP User: ${this.emailConfig.smtpUser}`);
      this.logger.log(`SMTP Secure: ${this.emailConfig.smtpSecure}`);
    } else {
      this.logger.warn(
        'SMTP credentials not configured. EmailService will not be available for direct SMTP sending.',
      );
      // Không tạo transporter, sẽ throw error khi gọi sendEmail
    }
  }

  /**
   * Tạo transporter cho Nodemailer
   */
  private createTransporter(): nodemailer.Transporter {
    // Sử dụng cấu hình từ ConfigService với tên biến đúng
    const smtpHost = this.emailConfig.smtpHost || 'smtp.gmail.com';
    const smtpPort = this.emailConfig.smtpPort || 587;
    const smtpUser = this.emailConfig.smtpUser;
    const smtpPass = this.emailConfig.smtpPass;
    const smtpSecure = this.emailConfig.smtpSecure;

    this.logger.debug(`Creating transporter with config:`, {
      host: smtpHost,
      port: smtpPort,
      secure: smtpSecure,
      user: smtpUser ? smtpUser.substring(0, 3) + '***' : 'not set',
    });

    return nodemailer.createTransport({
      host: smtpHost,
      port: smtpPort,
      secure: smtpSecure, // Sử dụng giá trị từ MAIL_SECURE
      auth: {
        user: smtpUser,
        pass: smtpPass,
      },
    });
  }

  /**
   * Gửi email thông qua SMTP.
   * @param sendEmailDto - Dữ liệu email (to, subject, body).
   * @returns Promise chứa kết quả gửi email hoặc ném lỗi nếu thất bại.
   */
  async sendEmail(sendEmailDto: SendEmailDto): Promise<any> {
    this.logger.log(`Sending email via SMTP to ${sendEmailDto.to}`);

    // Kiểm tra xem transporter có được khởi tạo không
    if (!this.transporter) {
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        'SMTP not configured. Please set MAIL_USERNAME and MAIL_PASSWORD environment variables.',
      );
    }

    try {
      // Chuẩn bị dữ liệu email
      const mailOptions = {
        from: this.emailConfig.defaultFrom || this.emailConfig.smtpUser,
        to: sendEmailDto.to,
        subject: sendEmailDto.subject,
        html: sendEmailDto.body,
      };

      // Gửi email
      const result = await this.transporter.sendMail(mailOptions);

      this.logger.log(
        `Successfully sent email to ${sendEmailDto.to}. Message ID: ${result.messageId}`,
      );

      return {
        success: true,
        messageId: result.messageId,
        response: result.response,
        envelope: result.envelope,
      };
    } catch (error) {
      this.logger.error(
        `Error sending email to ${sendEmailDto.to}: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        ErrorCode.EMAIL_SENDING_ERROR,
        `Failed to send email via SMTP: ${error.message}`,
      );
    }
  }

  /**
   * Kiểm tra kết nối SMTP
   * @returns Promise<boolean> - true nếu kết nối thành công
   */
  async verifyConnection(): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn(
        'SMTP transporter not initialized. Cannot verify connection.',
      );
      return false;
    }

    try {
      await this.transporter.verify();
      this.logger.log('SMTP connection verified successfully');
      return true;
    } catch (error) {
      this.logger.error(
        `SMTP connection verification failed: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
