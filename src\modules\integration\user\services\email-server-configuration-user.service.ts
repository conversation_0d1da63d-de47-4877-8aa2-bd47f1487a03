import {
  BadRequestException,
  Injectable,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import {
  CreateEmailServerDto,
  TestEmailServerDto,
  UpdateEmailServerDto,
  TestEmailServerWithConfigDto,
  EmailServerQueryDto,
} from '../dto';
import { EmailServerConfiguration } from '@modules/integration/entities';
import * as nodemailer from 'nodemailer';
import { EncryptionService } from '@shared/services/encryption.service';
import { EmailServerConfigurationRepository } from '../../repositories/email-server-configuration.repository';
import { PaginatedResult } from '@common/response';

@Injectable()
export class EmailServerConfigurationUserService {
  private readonly logger = new Logger(
    EmailServerConfigurationUserService.name,
  );

  constructor(
    private readonly emailServerRepo: EmailServerConfigurationRepository,
    private readonly encryptionService: EncryptionService,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách cấu hình máy chủ email của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách cấu hình máy chủ email
   */
  async findAllByUserId(userId: number): Promise<EmailServerConfiguration[]> {
    const emailServers = await this.emailServerRepo.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });

    // Che mật khẩu trước khi trả về
    return emailServers.map((server) => {
      const { password, ...serverWithoutPassword } = server;
      return {
        ...serverWithoutPassword,
        password: '********',
      } as EmailServerConfiguration;
    });
  }

  /**
   * Lấy danh sách cấu hình máy chủ email của người dùng với phân trang
   * @param queryDto Tham số truy vấn
   * @param userId ID của người dùng
   * @returns Danh sách cấu hình máy chủ email với phân trang
   */
  async findAllPaginated(
    queryDto: EmailServerQueryDto,
    userId: number,
  ): Promise<PaginatedResult<EmailServerConfiguration>> {
    try {
      const { page = 1, limit = 10, search, isActive } = queryDto;
      const skip = (page - 1) * limit;

      // Tạo query builder
      const queryBuilder = this.emailServerRepo.createQueryBuilder('email_server');

      // Thêm điều kiện lọc theo userId
      queryBuilder.andWhere('email_server.userId = :userId', { userId });

      // Thêm điều kiện lọc theo trạng thái hoạt động nếu có
      if (isActive !== undefined) {
        queryBuilder.andWhere('email_server.isActive = :isActive', { isActive });
      }

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere(
          '(email_server.serverName ILIKE :search OR email_server.host ILIKE :search)',
          { search: `%${search}%` }
        );
      }

      // Sắp xếp theo thời gian tạo mới nhất
      queryBuilder.orderBy('email_server.createdAt', 'DESC');

      // Áp dụng phân trang
      queryBuilder.skip(skip).take(limit);

      // Lấy dữ liệu và tổng số bản ghi
      const [emailServers, totalItems] = await queryBuilder.getManyAndCount();

      // Che mật khẩu trước khi trả về
      const items = emailServers.map((server) => {
        const { password, ...serverWithoutPassword } = server;
        return {
          ...serverWithoutPassword,
          password: '********',
        } as EmailServerConfiguration;
      });

      // Tạo metadata phân trang
      const meta = {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      };

      return { items, meta };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách cấu hình máy chủ email có phân trang: ${error.message}`);
      throw new BadRequestException(
        `Không thể lấy danh sách cấu hình máy chủ email: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của cấu hình máy chủ email
   */
  async findOne(id: number, userId: number): Promise<EmailServerConfiguration> {
    const emailServer = await this.emailServerRepo.findOne({
      where: { id, userId },
    });

    if (!emailServer) {
      throw new NotFoundException(
        `Không tìm thấy cấu hình máy chủ email với ID ${id}`,
      );
    }

    // Không giải mã mật khẩu khi trả về chi tiết
    // Chỉ trả về thông tin cơ bản, không bao gồm mật khẩu thật
    const { password, ...serverWithoutPassword } = emailServer;
    return {
      ...serverWithoutPassword,
      password: '********',
    } as EmailServerConfiguration;
  }

  /**
   * Lấy thông tin chi tiết của một cấu hình máy chủ email bao gồm mật khẩu đã giải mã
   * @param id ID của cấu hình máy chủ email
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết của cấu hình máy chủ email với mật khẩu đã giải mã
   * @private Phương thức này chỉ được sử dụng nội bộ
   */
  private async findOneWithDecryptedPassword(
    id: number,
    userId: number,
  ): Promise<EmailServerConfiguration> {
    const emailServer = await this.emailServerRepo.findOne({
      where: { id, userId },
    });

    if (!emailServer) {
      throw new NotFoundException(
        `Không tìm thấy cấu hình máy chủ email với ID ${id}`,
      );
    }

    try {
      // Giải mã mật khẩu
      if (emailServer.password) {
        emailServer.password = this.encryptionService.decrypt(
          emailServer.password,
        );
      }

      return emailServer;
    } catch (error) {
      this.logger.error(
        `Lỗi khi giải mã mật khẩu cho máy chủ email ID ${id}: ${error.message}`,
      );
      // Trả về đối tượng gốc nếu không thể giải mã
      return emailServer;
    }
  }

  /**
   * Tạo mới cấu hình máy chủ email
   * @param createEmailServerDto Thông tin cấu hình máy chủ email cần tạo
   * @param userId ID của người dùng
   * @returns Cấu hình máy chủ email đã được tạo
   */
  async create(
    createEmailServerDto: CreateEmailServerDto,
    userId: number,
  ): Promise<EmailServerConfiguration> {
    try {
      const now = Date.now();

      // Tạo bản sao của DTO để không thay đổi đối tượng gốc
      const dtoWithEncryptedPassword = { ...createEmailServerDto };

      // Mã hóa mật khẩu trước khi lưu vào database
      if (dtoWithEncryptedPassword.password) {
        dtoWithEncryptedPassword.password = this.encryptionService.encrypt(
          dtoWithEncryptedPassword.password,
        );
      }

      // Tạo đối tượng cấu hình máy chủ email
      const emailServer = this.emailServerRepo.create({
        ...dtoWithEncryptedPassword,
        userId,
        useStartTls: dtoWithEncryptedPassword.useStartTls ?? false,
        isActive: dtoWithEncryptedPassword.isActive ?? true,
        createdAt: now,
        updatedAt: now,
      });

      // Lưu vào database
      const savedServer = await this.emailServerRepo.save(emailServer);

      // Trả về đối tượng đã lưu nhưng che mật khẩu
      const { password, ...serverWithoutPassword } = savedServer;
      return {
        ...serverWithoutPassword,
        password: '********',
      } as EmailServerConfiguration;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo cấu hình máy chủ email: ${error.message}`);
      throw new BadRequestException(
        `Không thể tạo cấu hình máy chủ email: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param updateEmailServerDto Thông tin cần cập nhật
   * @param userId ID của người dùng
   * @returns Cấu hình máy chủ email đã được cập nhật
   */
  async update(
    id: number,
    updateEmailServerDto: UpdateEmailServerDto,
    userId: number,
  ): Promise<EmailServerConfiguration> {
    try {
      // Lấy thông tin gốc từ database (không phải phiên bản đã che mật khẩu)
      const originalServer = await this.emailServerRepo.findOne({
        where: { id, userId },
      });

      if (!originalServer) {
        throw new NotFoundException(
          `Không tìm thấy cấu hình máy chủ email với ID ${id}`,
        );
      }

      // Tạo bản sao của DTO để không thay đổi đối tượng gốc
      const dtoWithEncryptedPassword = { ...updateEmailServerDto };

      // Mã hóa mật khẩu mới nếu có
      if (dtoWithEncryptedPassword.password) {
        dtoWithEncryptedPassword.password = this.encryptionService.encrypt(
          dtoWithEncryptedPassword.password,
        );
      }

      // Cập nhật thông tin
      Object.assign(originalServer, {
        ...dtoWithEncryptedPassword,
        updatedAt: Date.now(),
      });

      // Lưu vào database
      const updatedServer = await this.emailServerRepo.save(originalServer);

      // Trả về đối tượng đã cập nhật nhưng che mật khẩu
      const { password, ...serverWithoutPassword } = updatedServer;
      return {
        ...serverWithoutPassword,
        password: '********',
      } as EmailServerConfiguration;
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật cấu hình máy chủ email ID ${id}: ${error.message}`,
      );
      throw new BadRequestException(
        `Không thể cập nhật cấu hình máy chủ email: ${error.message}`,
      );
    }
  }

  /**
   * Xóa cấu hình máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param userId ID của người dùng
   * @returns Thông báo kết quả
   */
  async remove(id: number, userId: number): Promise<{ message: string }> {
    const emailServer = await this.emailServerRepo.findOne({
      where: { id, userId },
    });

    if (!emailServer) {
      throw new NotFoundException(
        `Không tìm thấy cấu hình máy chủ email với ID ${id}`,
      );
    }

    await this.emailServerRepo.remove(emailServer);

    return { message: 'Xóa cấu hình máy chủ email thành công' };
  }

  /**
   * Kiểm tra kết nối máy chủ email
   * @param id ID của cấu hình máy chủ email
   * @param testEmailServerDto Thông tin kiểm tra
   * @param userId ID của người dùng
   * @returns Kết quả kiểm tra
   */
  async testConnection(
    id: number,
    testEmailServerDto: TestEmailServerDto,
    userId: number,
  ): Promise<{ success: boolean; message: string; details?: any }> {
    // Lấy thông tin máy chủ email với mật khẩu đã giải mã
    const emailServer = await this.findOneWithDecryptedPassword(id, userId);

    try {
      // Tạo transporter
      const transporter = nodemailer.createTransport({
        host: emailServer.host,
        port: emailServer.port,
        secure: emailServer.useSsl,
        requireTLS: emailServer.useStartTls,
        auth: {
          user: emailServer.username,
          pass: emailServer.password, // Sử dụng mật khẩu đã giải mã
        },
        ...emailServer.additionalSettings,
      });

      // Kiểm tra kết nối
      await transporter.verify();

      // Gửi email kiểm tra
      const subject =
        testEmailServerDto.subject || 'Kiểm tra kết nối máy chủ email';
      await transporter.sendMail({
        from: emailServer.username,
        to: testEmailServerDto.recipientEmail,
        subject,
        text: `Đây là email kiểm tra kết nối từ máy chủ ${emailServer.serverName}. Nếu bạn nhận được email này, kết nối đã thành công.`,
        html: `<p>Đây là email kiểm tra kết nối từ máy chủ <strong>${emailServer.serverName}</strong>.</p><p>Nếu bạn nhận được email này, kết nối đã thành công.</p>`,
      });

      return {
        success: true,
        message: 'Kết nối thành công! Email kiểm tra đã được gửi.',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Kết nối thất bại!',
        details: error.message || 'Không thể kết nối đến máy chủ email.',
      };
    }
  }

  /**
   * Kiểm tra kết nối máy chủ email với cấu hình trực tiếp (không cần lưu vào database)
   * @param testEmailServerWithConfigDto Thông tin cấu hình và kiểm tra
   * @returns Kết quả kiểm tra
   */
  async testConnectionWithConfig(
    testEmailServerWithConfigDto: TestEmailServerWithConfigDto,
  ): Promise<{ success: boolean; message: string; details?: any }> {
    const { emailServerConfig, testInfo } = testEmailServerWithConfigDto;

    try {
      // Tạo transporter với cấu hình được cung cấp
      const transporter = nodemailer.createTransport({
        host: emailServerConfig.host,
        port: emailServerConfig.port,
        secure: emailServerConfig.useSsl,
        requireTLS: emailServerConfig.useStartTls,
        auth: {
          user: emailServerConfig.username,
          pass: emailServerConfig.password,
        },
        ...emailServerConfig.additionalSettings,
      });

      // Kiểm tra kết nối
      await transporter.verify();

      // Gửi email kiểm tra
      const subject = testInfo.subject || 'Kiểm tra kết nối máy chủ email';
      await transporter.sendMail({
        from: emailServerConfig.username,
        to: testInfo.recipientEmail,
        subject,
        text: `Đây là email kiểm tra kết nối từ máy chủ ${emailServerConfig.serverName}. Nếu bạn nhận được email này, kết nối đã thành công.`,
        html: `<p>Đây là email kiểm tra kết nối từ máy chủ <strong>${emailServerConfig.serverName}</strong>.</p><p>Nếu bạn nhận được email này, kết nối đã thành công.</p>`,
      });

      return {
        success: true,
        message: 'Kết nối thành công! Email kiểm tra đã được gửi.',
      };
    } catch (error) {
      this.logger.error(
        'Lỗi khi kiểm tra kết nối máy chủ email với cấu hình trực tiếp:',
        error,
      );
      return {
        success: false,
        message: 'Kết nối thất bại!',
        details: error.message || 'Không thể kết nối đến máy chủ email.',
      };
    }
  }
}
