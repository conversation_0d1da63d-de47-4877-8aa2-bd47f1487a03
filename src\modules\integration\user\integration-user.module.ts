import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import * as entities from '../entities';
import * as repositories from '../repositories';
import * as controllers from './controllers';
/**
 * <PERSON><PERSON><PERSON> quản lý tích hợp cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([...Object.values(entities)]),
    HttpModule
  ],
  controllers: Object.values(controllers),
  providers: [
    ...Object.values(repositories),
  ],
  exports: [
    ...Object.values(repositories),
  ],
})
export class IntegrationUserModule {}
