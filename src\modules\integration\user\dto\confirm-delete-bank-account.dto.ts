import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Length } from 'class-validator';

/**
 * DTO cho việc xác nhận xóa tài khoản ngân hàng
 */
export class ConfirmDeleteBankAccountDto {
  @ApiProperty({
    description: 'Mã OTP xác nhận',
    example: '123456'
  })
  @IsNotEmpty()
  @IsString()
  @Length(4, 8, { message: 'OTP phải có độ dài từ 4 đến 8 ký tự' })
  otp: string;
}
