import { Injectable, Logger } from '@nestjs/common';
import { ConfigService as NestConfigService } from '@nestjs/config';
import {
  AppConfig,
  DatabaseConfig,
  StorageConfig,
  AuthConfig,
  ServicesConfig,
  S3Config,
  FacebookConfig,
} from './interfaces';
import { ConfigType, CONFIG_CONSTANTS, Environment } from './constants';

/**
 * Service cung cấp các phương thức để truy cập cấu hình ứng dụng
 */
@Injectable()
export class ConfigService {
  private readonly logger = new Logger(ConfigService.name);
  private config: AppConfig;

  constructor(private readonly nestConfigService: NestConfigService) {
    this.initializeConfig();
  }

  /**
   * Khởi tạo cấu hình ứng dụng
   */
  private initializeConfig(): void {
    this.config = {
      port: this.nestConfigService.get<number>(
        'PORT',
        CONFIG_CONSTANTS.DEFAULTS.PORT,
      ),
      nodeEnv: this.nestConfigService.get<string>(
        'NODE_ENV',
        CONFIG_CONSTANTS.DEFAULTS.NODE_ENV,
      ),
      apiPrefix: this.nestConfigService.get<string>(
        'API_PREFIX',
        CONFIG_CONSTANTS.DEFAULTS.API_PREFIX,
      ),

      database: this.getDatabaseConfig(),
      storage: this.getStorageConfig(),
      auth: this.getAuthConfig(),
      services: this.getServicesConfig(),
      facebook: this.getFacebookConfig(),
      s3: this.getS3Config(),
    };

    this.logger.log(
      `Application configured for environment: ${this.config.nodeEnv}`,
    );
  }

  /**
   * Lấy cấu hình database
   */
  private getDatabaseConfig(): DatabaseConfig {
    return {
      host: this.nestConfigService.getOrThrow<string>('DB_HOST'),
      port: this.nestConfigService.get<number>('DB_PORT', 5432),
      username: this.nestConfigService.getOrThrow<string>('DB_USERNAME'),
      password: this.nestConfigService.getOrThrow<string>('DB_PASSWORD'),
      database: this.nestConfigService.getOrThrow<string>('DB_DATABASE'),
      ssl: this.nestConfigService.get<boolean>('DB_SSL', false),
    };
  }

  /*
   * Lấy cấu hình s3
   */
  private getS3Config(): S3Config {
    return {
      s3: {
        endpoint: this.nestConfigService.getOrThrow<string>('CF_R2_ENDPOINT'),
        region: this.nestConfigService.getOrThrow<string>('CF_R2_REGION'),
        accessKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_ACCESS_KEY'),
        secretAccessKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_SECRET_KEY'),
        bucketName: this.nestConfigService.getOrThrow<string>('CF_BUCKET_NAME'),
      },
      cdn: {
        url: this.nestConfigService.getOrThrow<string>('CDN_URL'),
        secretKey: this.nestConfigService.getOrThrow<string>('CDN_SECRET_KEY'),
      },
    };
  }

   /**
   * Lấy cấu hình facebook
   */
  private getFacebookConfig(): FacebookConfig {
    try {
      return {
        appId: this.nestConfigService.get<string>('FACEBOOK_APP_ID', 'mock_facebook_app_id'),
        appSecret: this.nestConfigService.get<string>('FACEBOOK_APP_SECRET', 'mock_facebook_app_secret'),
        graphApiVersion: this.nestConfigService.get<string>('FACEBOOK_GRAPH_API_VERSION', 'v18.0'),
        redirectUri: this.nestConfigService.get<string>('FACEBOOK_REDIRECT_URI', 'http://localhost:3000/auth/facebook/callback'),
      };
    } catch (error) {
      this.logger.warn('Sử dụng cấu hình Facebook mặc định vì thiếu biến môi trường');
      return {
        appId: 'mock_facebook_app_id',
        appSecret: 'mock_facebook_app_secret',
        graphApiVersion: 'v18.0',
        redirectUri: 'http://localhost:3000/auth/facebook/callback',
      };
    }
  }

  /**
   * Lấy cấu hình storage
   */
  private getStorageConfig(): StorageConfig {
    return {
      cloudflare: {
        region: this.nestConfigService.getOrThrow<string>('CF_R2_REGION'),
        accessKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_ACCESS_KEY'),
        secretKey:
          this.nestConfigService.getOrThrow<string>('CF_R2_SECRET_KEY'),
        endpoint: this.nestConfigService.getOrThrow<string>('CF_R2_ENDPOINT'),
        bucketName: this.nestConfigService.getOrThrow<string>('CF_BUCKET_NAME'),
      },
      cdn: {
        url: this.nestConfigService.getOrThrow<string>('CDN_URL'),
        secretKey: this.nestConfigService.getOrThrow<string>('CDN_SECRET_KEY'),
      },
    };
  }

  /**
   * Lấy cấu hình authentication
   */
  private getAuthConfig(): AuthConfig {
    return {
      jwt: {
        secret: this.nestConfigService.getOrThrow<string>('JWT_SECRET'),
        expirationTime: this.nestConfigService.get<string>(
          'JWT_EXPIRATION_TIME',
          '1d',
        ),
        refreshSecret: this.nestConfigService.get<string>('JWT_REFRESH_SECRET'),
        refreshExpirationTime: this.nestConfigService.get<string>(
          'JWT_REFRESH_EXPIRATION_TIME',
          '7d',
        ),
      },
    };
  }

  /**
   * Lấy cấu hình external services
   */
  private getServicesConfig(): ServicesConfig {
    return {
      openai: {
        apiKey: this.nestConfigService.getOrThrow<string>('OPENAI_API_KEY'),
        organizationId: this.nestConfigService.get<string>(
          'OPENAI_ORGANIZATION_ID',
        ),
      },
      anthropic: {
        apiKey: this.nestConfigService.get<string>('ANTHROPIC_API_KEY', ''),
      },
      googleAI: {
        apiKey: this.nestConfigService.get<string>('GOOGLE_AI_API_KEY', ''),
      },
      deepseek: {
        apiKey: this.nestConfigService.get<string>('DEEPSEEK_API_KEY', ''),
      },
      metaAI: {
        apiKey: this.nestConfigService.get<string>('META_AI_API_KEY', ''),
      },
      redis: {
        url: this.nestConfigService.getOrThrow<string>('REDIS_URL'),
        password: this.nestConfigService.get<string>('REDIS_PASSWORD'),
      },
    };
  }

  /**
   * Lấy toàn bộ cấu hình ứng dụng
   */
  get appConfig(): AppConfig {
    return this.config;
  }

  /**
   * Lấy cấu hình theo loại
   * @param type Loại cấu hình
   */
  getConfig<T>(type: ConfigType): T {
    switch (type) {
      case ConfigType.App:
        return {
          port: this.config.port,
          nodeEnv: this.config.nodeEnv,
          apiPrefix: this.config.apiPrefix,
        } as unknown as T;
      case ConfigType.Database:
        return this.config.database as unknown as T;
      case ConfigType.Storage:
        return this.config.storage as unknown as T;
      case ConfigType.Auth:
        return this.config.auth as unknown as T;
      case ConfigType.Services:
        return this.config.services as unknown as T;
      case ConfigType.S3:
        return this.config.s3 as unknown as T;
      default:
        throw new Error(`Unknown config type: ${type}`);
    }
  }

  /**
   * Kiểm tra xem ứng dụng có đang chạy trong môi trường development không
   */
  isDevelopment(): boolean {
    return this.config.nodeEnv === Environment.Development;
  }

  /**
   * Kiểm tra xem ứng dụng có đang chạy trong môi trường production không
   */
  isProduction(): boolean {
    return this.config.nodeEnv === Environment.Production;
  }

  /**
   * Kiểm tra xem ứng dụng có đang chạy trong môi trường test không
   */
  isTest(): boolean {
    return this.config.nodeEnv === Environment.Test;
  }
}
