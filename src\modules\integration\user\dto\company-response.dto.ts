import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum đại diện cho các trạng thái có thể có của công ty
 */
export enum CompanyStatus {
  PENDING = 'Pending',
  ACTIVE = 'Active',
  SUSPENDED = 'Suspended',
  TERMINATED = 'Terminated',
  CANCELLED = 'Cancelled',
  FRAUD = 'Fraud',
}

/**
 * DTO cho việc trả về thông tin công ty
 */
export class CompanyResponseDto {
  @ApiProperty({
    description: 'ID của công ty',
    example: '123456'
  })
  id: string;

  @ApiProperty({
    description: 'Tên đầy đủ của công ty',
    example: 'Công ty TNHH Thương mại Dịch vụ ABC'
  })
  fullName: string;

  @ApiProperty({
    description: 'Tên viết tắt của công ty',
    example: 'ABC'
  })
  shortName: string;

  @ApiProperty({
    description: 'Trạng thái của công ty',
    enum: CompanyStatus,
    example: CompanyStatus.ACTIVE
  })
  status: CompanyStatus;

  @ApiProperty({
    description: 'Ngày tạo',
    example: '2023-01-01 00:00:00'
  })
  createdAt: string;

  @ApiProperty({
    description: 'Ngày cập nhật',
    example: '2023-01-01 00:00:00'
  })
  updatedAt: string;
}
