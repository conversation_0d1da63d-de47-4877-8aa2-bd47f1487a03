import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho việc trả về thông tin website
 */
export class WebsiteResponseDto {
  @ApiProperty({
    description: 'ID của website',
    example: '86c238b5-ab20-45ee-9eb2-fa75a40e9751',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Tên miền hoặc địa chỉ host của website',
    example: 'redai.vn',
  })
  @Expose()
  host: string;

  @ApiProperty({
    description: 'Trạng thái xác minh của website (TRUE nếu đã xác minh)',
    example: false,
  })
  @Expose()
  verify: boolean;

  @ApiProperty({
    description: 'ID agent được kết nối với website',
    example: null,
    nullable: true,
  })
  @Expose()
  agentId: string | null;

  @ApiProperty({
    description: 'Tên của agent đ<PERSON><PERSON><PERSON> kết nối với website',
    example: null,
    nullable: true,
  })
  @Expose()
  agentName: string | null;

  @ApiProperty({
    description: 'URL CDN của logo website',
    example: null,
    nullable: true,
  })
  @Expose()
  logo: string | null;

  @ApiProperty({
    description: 'Thời điểm tạo bản ghi',
    example: '2025-05-25T09:03:11.444Z',
  })
  @Expose()
  createdAt: Date;
}
