import { IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { ProviderShipmentType } from '../../../constants/provider-shipment-type.enum';

/**
 * DTO cho cấu hình GHN
 */
export class GHNConfigDto {
  @ApiProperty({ description: 'API Token từ GHN dashboard' })
  @IsNotEmpty()
  @IsString()
  token: string;

  @ApiProperty({ description: 'Shop ID từ GHN' })
  @IsNotEmpty()
  shopId: number;

  @ApiProperty({ description: 'Môi trường', enum: ['staging', 'production'], required: false })
  @IsOptional()
  @IsString()
  environment?: 'staging' | 'production';
}

/**
 * DTO cho cấu hình GHTK
 */
export class GHTKConfigDto {
  @ApiProperty({ description: 'API Token từ GHTK dashboard' })
  @IsNotEmpty()
  @IsString()
  apiToken: string;

  @ApiProperty({ description: 'Partner Code (mã đối tác)' })
  @IsNotEmpty()
  @IsString()
  partnerCode: string;

  @ApiProperty({ description: 'Môi trường', enum: ['staging', 'production'], required: false })
  @IsOptional()
  @IsString()
  environment?: 'staging' | 'production';
}

/**
 * DTO cho cấu hình Ahamove
 */
export class AhamoveConfigDto {
  @ApiProperty({ description: 'API Key từ partner registration' })
  @IsNotEmpty()
  @IsString()
  apiKey: string;

  @ApiProperty({ description: 'Số điện thoại tài khoản' })
  @IsNotEmpty()
  @IsString()
  mobile: string;

  @ApiProperty({ description: 'JWT Token (tùy chọn)', required: false })
  @IsOptional()
  @IsString()
  token?: string;

  @ApiProperty({ description: 'Refresh Token (tùy chọn)', required: false })
  @IsOptional()
  @IsString()
  refreshToken?: string;

  @ApiProperty({ description: 'Thời gian hết hạn token', required: false })
  @IsOptional()
  tokenExpiry?: number;

  @ApiProperty({ description: 'Môi trường', enum: ['staging', 'production'], required: false })
  @IsOptional()
  @IsString()
  environment?: 'staging' | 'production';
}

/**
 * DTO cho cấu hình J&T Express
 */
export class JTConfigDto {
  @ApiProperty({ description: 'Tên đăng nhập từ J&T dashboard' })
  @IsNotEmpty()
  @IsString()
  username: string;

  @ApiProperty({ description: 'API Key từ J&T dashboard' })
  @IsNotEmpty()
  @IsString()
  apiKey: string;

  @ApiProperty({ description: 'Secret key để tạo signature' })
  @IsNotEmpty()
  @IsString()
  secretKey: string;

  @ApiProperty({ description: 'Môi trường', enum: ['staging', 'production'], required: false })
  @IsOptional()
  @IsString()
  environment?: 'staging' | 'production';
}

/**
 * DTO chính để tạo cấu hình nhà cung cấp vận chuyển
 */
export class CreateShipmentProviderDto {
  @ApiProperty({ description: 'Tên hiển thị của cấu hình', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Loại nhà cung cấp vận chuyển',
    enum: ProviderShipmentType,
    example: ProviderShipmentType.GHN
  })
  @IsNotEmpty()
  @IsEnum(ProviderShipmentType)
  type: ProviderShipmentType;

  @ApiProperty({
    description: 'Cấu hình chi tiết theo từng nhà cung cấp',
    oneOf: [
      { $ref: '#/components/schemas/GHNConfigDto' },
      { $ref: '#/components/schemas/GHTKConfigDto' },
      { $ref: '#/components/schemas/AhamoveConfigDto' },
      { $ref: '#/components/schemas/JTConfigDto' }
    ]
  })
  @IsNotEmpty()
  @IsObject()
  config: GHNConfigDto | GHTKConfigDto | AhamoveConfigDto | JTConfigDto;
}
