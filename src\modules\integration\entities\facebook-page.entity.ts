import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';

/**
 * Entity đại diện cho bảng facebook_page trong cơ sở dữ liệu
 * Bảng quản lý thông tin trang Facebook được kết nối với agent
 */
@Entity('facebook_page')
@Unique('facebook_page_pk_2', ['facebookPersonalId', 'facebookPageId'])
export class FacebookPage {
  /**
   * ID duy nhất của bản ghi, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * ID duy nhất của trang Facebook
   */
  @Column({ name: 'facebook_page_id', length: 255, nullable: false })
  facebookPageId: string;

  /**
   * ID tài khoản Facebook cá nhân liên kết (UUID)
   */
  @Column({ name: 'facebook_personal_id', type: 'uuid', nullable: false })
  facebookPersonalId: string;

  /**
   * Access token của trang Facebook
   */
  @Column({ name: 'page_access_token', length: 1000, nullable: true })
  pageAccessToken: string;

  /**
   * Tên trang Facebook
   */
  @Column({ name: 'page_name', type: 'text', nullable: true })
  pageName: string;

  /**
   * Trạng thái hoạt động của trang
   */
  @Column({ name: 'is_active', type: 'boolean', nullable: true })
  isActive: boolean;

  /**
   * URL avatar của trang Facebook
   */
  @Column({ name: 'avatar_page', length: 255, nullable: true })
  avatarPage: string;

  /**
   * Trạng thái lỗi của trang Facebook
   */
  @Column({ name: 'is_error', type: 'boolean', nullable: true })
  isError: boolean;

  /**
   * ID agent được kết nối với trang Facebook
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId: string | null;

  /**
   * Thời điểm xóa mềm (Unix timestamp)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @CreateDateColumn({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
  })
  createdAt: number;
}
