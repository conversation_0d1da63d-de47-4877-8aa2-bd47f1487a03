import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response trả về thông tin cấu hình máy chủ email
 */
export class EmailServerResponseDto {
  @ApiProperty({
    description: 'ID của cấu hình máy chủ email',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng sở hữu cấu hình',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'Tên hiển thị của cấu hình, ví dụ: "Mailgun Server #1" hoặc "AWS SES"',
    example: 'Gmail SMTP',
  })
  serverName: string;

  @ApiProperty({
    description: 'Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…',
    example: 'smtp.gmail.com',
  })
  host: string;

  @ApiProperty({
    description: '<PERSON>ổng SMTP, ví dụ: 465, 587, …',
    example: 587,
  })
  port: number;

  @ApiProperty({
    description: 'Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng)',
    example: '<EMAIL>',
  })
  username: string;

  @ApiProperty({
    description: 'Xác định có sử dụng SSL/TLS hay không',
    example: true,
  })
  useSsl: boolean;

  @ApiProperty({
    description: 'Xác định có sử dụng STARTTLS hay không',
    example: false,
  })
  useStartTls: boolean;

  @ApiProperty({
    description: 'Trạng thái hoạt động của cấu hình',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.',
    example: { auth: 'login', tls: { rejectUnauthorized: false } },
  })
  additionalSettings: Record<string, any>;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: *************,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: *************,
  })
  updatedAt: number;
}

/**
 * DTO cho response trả về kết quả kiểm tra kết nối máy chủ email
 */
export class TestEmailServerResponseDto {
  @ApiProperty({
    description: 'Kết quả kiểm tra kết nối',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Kết nối thành công! Email kiểm tra đã được gửi.',
  })
  message: string;

  @ApiProperty({
    description: 'Thông tin chi tiết (nếu có lỗi)',
    example: null,
    required: false,
  })
  details?: any;
}
