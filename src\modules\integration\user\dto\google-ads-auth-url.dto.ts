import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUrl } from 'class-validator';

/**
 * DTO cho response URL xác thực Google Ads
 */
export class GoogleAdsAuthUrlResponseDto {
  @ApiProperty({
    description: 'URL xác thực Google Ads',
    example: 'https://accounts.google.com/o/oauth2/auth?client_id=...',
  })
  @IsUrl()
  url: string;

  @ApiProperty({
    description: 'State token để xác thực callback',
    example: 'abc123xyz456',
  })
  @IsString()
  state: string;
}

/**
 * DTO cho request URL xác thực Google Ads
 */
export class GoogleAdsAuthUrlRequestDto {
  @ApiProperty({
    description: 'URL callback sau khi xác thực',
    example: 'https://example.com/callback',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  redirectUri?: string;
}
