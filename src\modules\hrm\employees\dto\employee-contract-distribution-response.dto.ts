import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin phân bố nhân viên theo loại hợp đồng
 */
export class ContractDistributionItemDto {
  /**
   * Loại hợp đồng
   * @example "full_time"
   */
  @ApiProperty({
    description: 'Loại hợp đồng',
    example: 'full_time',
  })
  contractType: string;

  /**
   * Tên hiển thị loại hợp đồng
   * @example "Toàn thời gian"
   */
  @ApiProperty({
    description: 'Tên hiển thị loại hợp đồng',
    example: 'Toàn thời gian',
  })
  contractTypeName: string;

  /**
   * Số lượng nhân viên có loại hợp đồng này
   * @example 95
   */
  @ApiProperty({
    description: 'Số lượng nhân viên có loại hợp đồng này',
    example: 95,
  })
  employeeCount: number;

  /**
   * Tỷ lệ phần trăm so với tổng số nhân viên
   * @example 76.0
   */
  @ApiProperty({
    description: 'Tỷ lệ phần trăm so với tổng số nhân viên',
    example: 76.0,
  })
  percentage: number;
}

/**
 * DTO cho phản hồi phân bố nhân viên theo loại hợp đồng
 */
export class EmployeeContractDistributionResponseDto {
  /**
   * Tổng số nhân viên
   * @example 125
   */
  @ApiProperty({
    description: 'Tổng số nhân viên',
    example: 125,
  })
  totalEmployees: number;

  /**
   * Danh sách phân bố theo loại hợp đồng
   */
  @ApiProperty({
    description: 'Danh sách phân bố nhân viên theo loại hợp đồng',
    type: [ContractDistributionItemDto],
  })
  contracts: ContractDistributionItemDto[];

  /**
   * Số lượng nhân viên chưa có hợp đồng
   * @example 0
   */
  @ApiProperty({
    description: 'Số lượng nhân viên chưa có hợp đồng',
    example: 0,
  })
  noContractEmployees: number;

  /**
   * Tỷ lệ phần trăm nhân viên chưa có hợp đồng
   * @example 0.0
   */
  @ApiProperty({
    description: 'Tỷ lệ phần trăm nhân viên chưa có hợp đồng',
    example: 0.0,
  })
  noContractPercentage: number;
}
