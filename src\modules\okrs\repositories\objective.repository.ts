import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Objective } from '../entities/objective.entity';
import { OkrCycle } from '../entities/okr-cycle.entity';
import { ObjectiveQueryDto } from '../dto/objective/objective-query.dto';
import { PaginatedResult } from '@/common/response/api-response-dto';

/**
 * Repository cho mục tiêu với tenant isolation đơn giản
 */
@Injectable()
export class ObjectiveRepository {
  private readonly logger = new Logger(ObjectiveRepository.name);

  constructor(
    @InjectRepository(Objective)
    private readonly repository: Repository<Objective>,
    @InjectRepository(OkrCycle)
    private readonly cycleRepository: Repository<OkrCycle>,
  ) {}

  /**
   * Helper function để format date từ Date object hoặc string
   */
  private formatDate(date: Date | string): string {
    if (date instanceof Date) {
      return date.toISOString().split('T')[0];
    }
    if (typeof date === 'string') {
      // Nếu đã là format YYYY-MM-DD thì trả về luôn
      if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return date;
      }
      // Nếu là string khác, parse thành Date rồi format
      return new Date(date).toISOString().split('T')[0];
    }
    return '';
  }

  /**
   * Tìm tất cả mục tiêu với phân trang và lọc
   * @param tenantId ID tenant (required for tenant isolation)
   * @param query Tham số truy vấn
   * @returns Danh sách phân trang các mục tiêu
   */
  async findAll(
    tenantId: number,
    query: ObjectiveQueryDto,
  ): Promise<PaginatedResult<Objective & { cycle?: { id: number; name: string; startDate: string; endDate: string; status: string } | null }>> {
    const {
      page = 1,
      limit = 10,
      search,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
      type,
      cycleId,
      ownerId,
      departmentId,
    } = query;

    const queryBuilder = this.repository.createQueryBuilder('objective');

    // Left join với OKR cycle để lấy thông tin chu kỳ
    queryBuilder.leftJoin(
      'okr_cycles',
      'cycle',
      'cycle.id = objective.cycleId AND (cycle.tenantId = :tenantId OR cycle.tenantId IS NULL)',
      { tenantId }
    );

    // Select tất cả fields của objective và cycle
    queryBuilder.select([
      'objective.id',
      'objective.title',
      'objective.description',
      'objective.ownerId',
      'objective.departmentId',
      'objective.cycleId',
      'objective.type',
      'objective.progress',
      'objective.status',
      'objective.createdAt',
      'objective.updatedAt',
      'objective.tenantId',
      'cycle.id as cycle_id',
      'cycle.name as cycle_name',
      'cycle.start_date as cycle_start_date',
      'cycle.end_date as cycle_end_date',
      'cycle.status as cycle_status'
    ]);

    // Add tenantId filtering - REQUIRED for tenant isolation
    queryBuilder.andWhere('objective.tenantId = :tenantId', { tenantId });

    // Áp dụng bộ lọc nếu được cung cấp
    if (type) {
      queryBuilder.andWhere('objective.type = :type', { type });
    }

    if (cycleId) {
      queryBuilder.andWhere('objective.cycleId = :cycleId', { cycleId });
    }

    if (ownerId) {
      queryBuilder.andWhere('objective.ownerId = :ownerId', { ownerId });
    }

    if (departmentId) {
      queryBuilder.andWhere('objective.departmentId = :departmentId', {
        departmentId,
      });
    }



    // Áp dụng bộ lọc tìm kiếm nếu được cung cấp
    if (search) {
      queryBuilder.andWhere(
        'objective.title ILIKE :search OR objective.description ILIKE :search',
        { search: `%${search}%` },
      );
    }

    // Áp dụng sắp xếp
    queryBuilder.orderBy(`objective.${sortBy}`, sortDirection);

    // Đếm tổng số items trước khi phân trang
    const totalItems = await queryBuilder.getCount();

    // Áp dụng phân trang và lấy objectives
    const objectives = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    // Lấy tất cả cycleIds unique
    const cycleIds = [...new Set(objectives.map(obj => obj.cycleId).filter(id => id !== null))];

    this.logger.debug(`Found cycleIds: ${JSON.stringify(cycleIds)} for tenantId: ${tenantId}`);

    // Lấy cycles theo cycleIds và tenantId
    const cycles = cycleIds.length > 0 ? await this.cycleRepository.find({
      where: {
        id: In(cycleIds),
        tenantId: tenantId
      }
    }) : [];

    this.logger.debug(`Found cycles: ${JSON.stringify(cycles.map(c => ({ id: c.id, name: c.name, tenantId: c.tenantId })))}`);


    // Tạo map để lookup cycles
    const cycleMap = new Map(cycles.map(cycle => [cycle.id, cycle]));

    // Kết hợp objectives với cycle info
    const items = objectives.map(objective => {
      if (!objective.cycleId || !cycleMap.has(objective.cycleId)) {
        return { ...objective, cycle: null };
      }

      const cycle = cycleMap.get(objective.cycleId)!;

      return {
        ...objective,
        cycle: {
          id: cycle.id,
          name: cycle.name,
          startDate: this.formatDate(cycle.startDate),
          endDate: this.formatDate(cycle.endDate),
          status: cycle.status || '',
        },
      };
    });

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm mục tiêu theo ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID mục tiêu
   * @returns Mục tiêu hoặc null nếu không tìm thấy
   */
  async findById(tenantId: number, id: number): Promise<Objective & { cycle?: { id: number; name: string; startDate: string; endDate: string; status: string } | null } | null> {
    // Lấy objective trước
    const objective = await this.repository.findOne({
      where: { id, tenantId },
    });

    if (!objective) {
      return null;
    }

    // Lấy cycle info nếu có cycleId
    let cycle: { id: number; name: string; startDate: string; endDate: string; status: string } | null = null;
    if (objective.cycleId) {
      const cycleEntity = await this.cycleRepository.findOne({
        where: { id: objective.cycleId, tenantId },
      });

      if (cycleEntity) {
        cycle = {
          id: cycleEntity.id,
          name: cycleEntity.name,
          startDate: this.formatDate(cycleEntity.startDate),
          endDate: this.formatDate(cycleEntity.endDate),
          status: cycleEntity.status || '',
        };
      }
    }

    return {
      ...objective,
      cycle,
    };
  }

  /**
   * Tạo mới mục tiêu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param data Dữ liệu mục tiêu
   * @returns Mục tiêu đã tạo
   */
  async create(tenantId: number, data: Partial<Objective>): Promise<Objective> {
    const objective = this.repository.create({ ...data, tenantId });
    return this.repository.save(objective);
  }

  /**
   * Cập nhật mục tiêu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID mục tiêu
   * @param data Dữ liệu mục tiêu cập nhật
   * @returns Mục tiêu đã cập nhật hoặc null nếu không tìm thấy
   */
  async update(
    tenantId: number,
    id: number,
    data: Partial<Objective>,
  ): Promise<Objective & { cycle?: { id: number; name: string; startDate: string; endDate: string; status: string } | null } | null> {
    await this.repository.update({ id, tenantId }, data);
    return this.findById(tenantId, id);
  }

  /**
   * Xóa mục tiêu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID mục tiêu
   * @returns True nếu xóa thành công, false nếu không tìm thấy
   */
  async delete(tenantId: number, id: number): Promise<boolean> {
    const result = await this.repository.delete({ id, tenantId });
    return (result.affected ?? 0) > 0;
  }

  /**
   * Tìm mục tiêu theo ID chu kỳ
   * @param tenantId ID tenant (required for tenant isolation)
   * @param cycleId ID chu kỳ OKR
   * @returns Danh sách mục tiêu
   */
  async findByCycleId(tenantId: number, cycleId: number): Promise<Objective[]> {
    return this.repository.find({
      where: { cycleId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm mục tiêu theo ID chủ sở hữu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ownerId ID chủ sở hữu
   * @returns Danh sách mục tiêu
   */
  async findByOwnerId(tenantId: number, ownerId: number): Promise<Objective[]> {
    return this.repository.find({
      where: { ownerId, tenantId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Cập nhật tiến độ mục tiêu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param id ID mục tiêu
   * @param progress Giá trị tiến độ (0-100)
   * @returns Mục tiêu đã cập nhật hoặc null nếu không tìm thấy
   */
  async updateProgress(
    tenantId: number,
    id: number,
    progress: number,
  ): Promise<Objective & { cycle?: { id: number; name: string; startDate: string; endDate: string; status: string } | null } | null> {
    await this.repository.update(
      { id, tenantId },
      { progress, updatedAt: Date.now() },
    );
    return this.findById(tenantId, id);
  }

  /**
   * Tìm nhiều mục tiêu theo danh sách ID
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID mục tiêu
   * @returns Danh sách mục tiêu tìm thấy
   */
  async findByIds(tenantId: number, ids: number[]): Promise<Objective[]> {
    if (ids.length === 0) return [];

    return this.repository.find({
      where: {
        id: In(ids),
        tenantId,
      },
    });
  }

  /**
   * Xóa nhiều mục tiêu
   * @param tenantId ID tenant (required for tenant isolation)
   * @param ids Danh sách ID mục tiêu cần xóa
   * @returns Số lượng mục tiêu đã xóa thành công
   */
  async bulkDelete(tenantId: number, ids: number[]): Promise<number> {
    if (ids.length === 0) return 0;

    const result = await this.repository.delete({
      id: In(ids),
      tenantId,
    });

    return result.affected ?? 0;
  }
}
