import { IsE<PERSON>, IsNotEmpty, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ProviderShipmentType } from '../../../constants/provider-shipment-type.enum';
import { GHNConfigDto, GHTKConfigDto, AhamoveConfigDto, JTConfigDto } from './create-shipment-provider.dto';

/**
 * DTO để test kết nối với nhà cung cấp vận chuyển
 */
export class TestShipmentProviderDto {
  @ApiProperty({
    description: 'Loại nhà cung cấp vận chuyển',
    enum: ProviderShipmentType,
    example: ProviderShipmentType.GHN
  })
  @IsNotEmpty()
  @IsEnum(ProviderShipmentType)
  type: ProviderShipmentType;

  @ApiProperty({
    description: 'Cấu hình chi tiết theo từng nhà cung cấp',
    oneOf: [
      { $ref: '#/components/schemas/GHNConfigDto' },
      { $ref: '#/components/schemas/GHTKConfigDto' },
      { $ref: '#/components/schemas/AhamoveConfigDto' },
      { $ref: '#/components/schemas/JTConfigDto' }
    ]
  })
  @IsNotEmpty()
  @IsObject()
  config: GHNConfigDto | GHTKConfigDto | AhamoveConfigDto | JTConfigDto;
}

// TestShipmentProviderResponseDto đã được định nghĩa trong shipment-provider-response.dto.ts
