import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsInt,
  IsOptional,
  IsEmail,
  MinLength,
  MaxLength,
  Matches,
  IsBoolean,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo tài khoản người dùng cho nhân viên
 */
export class CreateUserForEmployeeDto {
  /**
   * ID của nhân viên (bắt buộc)
   * @example 33
   */
  @ApiProperty({ description: 'ID của nhân viên', example: 33 })
  @IsNotEmpty()
  @IsInt()
  @Type(() => Number)
  employeeId: number;

  /**
   * Tự động tạo mật khẩu (tùy chọn, mặc định false)
   * @example true
   */
  @ApiProperty({
    required: false,
    description: 'Tự động tạo mật khẩu',
    example: true,
    default: false
  })
  @IsOptional()
  @IsBoolean()
  autoGeneratePassword?: boolean;

  /**
   * M<PERSON>t khẩu (tùy chọn - bắt buộc khi autoGeneratePassword = false)
   * @example "Password@123"
   */
  @ApiProperty({
    required: false,
    description: 'Mật khẩu (bắt buộc khi autoGeneratePassword = false)',
    example: 'Password@123'
  })
  @ValidateIf((o) => !o.autoGeneratePassword)
  @IsNotEmpty({ message: 'Mật khẩu là bắt buộc khi autoGeneratePassword = false' })
  @IsString()
  @MinLength(8)
  @MaxLength(50)
  @Matches(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    {
      message:
        'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt',
    },
  )
  password?: string;

  /**
   * Địa chỉ email
   * @example "<EMAIL>"
   */
  @ApiProperty({ description: 'Địa chỉ email', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  @MaxLength(255)
  email: string;
}
