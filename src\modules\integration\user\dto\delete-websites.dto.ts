import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayMinSize } from 'class-validator';

/**
 * DTO cho việc xóa nhiều website cùng lúc
 */
export class DeleteWebsitesDto {
  /**
   * <PERSON>h sách UUID của các website cần xóa
   */
  @ApiProperty({
    description: 'Danh sách UUID của các website cần xóa',
    example: [
      'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      'b2c3d4e5-f6g7-8901-bcde-f23456789012'
    ],
    type: [String]
  })
  @IsArray({
    message: 'websiteIds phải là một mảng'
  })
  @ArrayMinSize(1, {
    message: '<PERSON><PERSON><PERSON> có ít nhất một website để xóa'
  })
  @IsUUID('4', {
    each: true,
    message: 'Mỗi website ID phải là UUID hợp lệ'
  })
  websiteIds: string[];
}
