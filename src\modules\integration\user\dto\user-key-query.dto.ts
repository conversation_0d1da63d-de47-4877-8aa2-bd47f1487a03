import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * DTO cho việc truy vấn danh sách API key của người dùng
 */
export class UserKeyQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên nhà cung cấp',
    required: false,
    example: 'openai'
  })
  @IsOptional()
  @IsString()
  providerKey?: string;
}
