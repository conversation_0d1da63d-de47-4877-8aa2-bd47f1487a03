import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';

/**
 * Entity đại diện cho bảng facebook_personal trong cơ sở dữ liệu
 * Bảng quản lý thông tin tài khoản Facebook cá nhân
 */
@Entity('facebook_personal')
@Unique('facebook_personal_pk_2', ['facebookPersonalId', 'userId'])
export class FacebookPersonal {
  /**
   * ID duy nhất của bản ghi, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * ID người dùng
   */
  @Column({ name: 'user_id', type: 'integer', nullable: false })
  userId: number;

  /**
   * Thời điểm xóa mềm (Unix timestamp)
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Access token của tài khoản Facebook
   */
  @Column({ name: 'access_token', length: 1000, nullable: true })
  accessToken: string;

  /**
   * ID tài khoản Facebook cá nhân
   */
  @Column({ name: 'facebook_personal_id', length: 255, nullable: false })
  facebookPersonalId: string;

  /**
   * Ngày hết hạn của token
   */
  @Column({ name: 'expiration_date', type: 'timestamp', nullable: true })
  expirationDate: Date;

  /**
   * Thời gian hết hạn của token (Unix timestamp)
   */
  @Column({
    name: 'expiration_date_unix',
    type: 'bigint',
    nullable: true,
    default: '(EXTRACT(epoch FROM now()) * (1000)::numeric)'
  })
  expirationDateUnix: number;
}
