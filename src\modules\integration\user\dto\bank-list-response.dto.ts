import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin ngân hàng
 */
export class BankListResponseDto {
  @ApiProperty({
    description: 'ID của ngân hàng',
    example: '1'
  })
  id: string;

  @ApiProperty({
    description: 'Tên ngân hàng',
    example: 'Vietcombank'
  })
  brandName: string;

  @ApiProperty({
    description: 'Tên đầy đủ ngân hàng',
    example: 'Ngân hàng TMCP Ngoại thương Việt Nam'
  })
  fullName: string;

  @ApiProperty({
    description: 'Tên ngắn gọn ngân hàng',
    example: 'VCB'
  })
  shortName: string;

  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VCB'
  })
  code: string;

  @ApiProperty({
    description: 'Bin ngân hàng',
    example: '970436'
  })
  bin: string;

  @ApiProperty({
    description: 'URL Logo ngân hàng',
    example: 'https://example.com/vcb-logo.png'
  })
  logoPath: string;

  @ApiProperty({
    description: 'URL Icon ngân hàng',
    example: 'https://example.com/vcb-icon.png'
  })
  icon: string;

  @ApiProperty({
    description: 'Trạng thái ngân hàng được hỗ trợ',
    example: true
  })
  active: boolean;
}
